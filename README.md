# 微信公众号自动化管理工具

<div align="center">

![Python](https://img.shields.io/badge/Python-3.8+-blue.svg)
![PySide6](https://img.shields.io/badge/PySide6-6.5+-green.svg)
![License](https://img.shields.io/badge/License-MIT-yellow.svg)
![Platform](https://img.shields.io/badge/Platform-Windows-lightgrey.svg)

一个功能强大的微信公众号自动化管理工具，支持批量关注、数据采集、信息管理等功能。

</div>

## ✨ 功能特性

### 🔧 核心功能
- **批量关注公众号** - 支持从CSV文件批量导入公众号进行关注
- **智能数据采集** - 自动采集公众号基本信息和文章数据
- **数据管理** - 完整的数据查看、搜索、筛选、导出功能
- **账号管理** - 便捷的公众号账号增删改查操作

### 🎨 界面特性
- **现代化UI** - 基于PySide6的美观界面设计
- **响应式布局** - 支持窗口大小调整和多屏幕适配
- **数据可视化** - 直观的数据统计和展示
- **操作便捷** - 简洁的操作流程和友好的用户体验

### ⚙️ 技术特性
- **模块化架构** - 清晰的代码结构，易于维护和扩展
- **配置管理** - 灵活的配置系统，支持个性化设置
- **错误处理** - 完善的异常处理和用户提示
- **日志记录** - 详细的操作日志，便于问题排查

## 📁 项目结构

```
Wechat-Auto/
├── main.py                 # 主程序入口
├── requirements.txt        # 依赖包列表
├── resource/               # 资源配置目录
│   ├── config.json        # 配置文件
│   ├── accounts.csv       # 账号数据文件
│   └── official_infos.json # 公众号信息文件
├── data/                   # 数据存储目录
│   ├── all.csv            # 汇总数据文件
│   └── *.csv              # 各账号采集数据
├── config/               # 配置模块
│   ├── __init__.py
│   └── settings.py       # 设置管理
├── core/                 # 核心业务逻辑
│   ├── __init__.py
│   ├── account_service.py    # 账号服务
│   ├── data_manager.py       # 数据管理
│   ├── official_service.py   # 公众号服务
│   ├── task_manager.py       # 任务管理
│   └── wechat_detector.py    # 微信检测
├── ui/                   # 用户界面
│   ├── __init__.py
│   ├── main_window.py        # 主窗口
│   ├── account_manager.py    # 账号管理界面
│   ├── collect_manager.py    # 采集管理界面
│   ├── follow_manager.py     # 关注管理界面
│   ├── data_viewer.py        # 数据查看界面
│   ├── settings_dialog.py    # 设置对话框
│   ├── message_box.py        # 消息框组件
│   └── components/           # UI组件
│       ├── __init__.py
│       ├── data_stats.py     # 数据统计组件
│       └── log_widget.py     # 日志组件
├── wechat/               # 微信自动化模块
│   ├── __init__.py
│   ├── auto_process/         # 自动化处理
│   └── wechat_ui_api/        # 微信UI接口
└── wechat/               # 微信自动化模块
    ├── __init__.py
    ├── auto_process/         # 自动化处理
    └── wechat_ui_api/        # 微信UI接口
```

## 🚀 快速开始

### 环境要求

- **操作系统**: Windows 10/11
- **Python**: 3.8 或更高版本
- **微信PC版**: 最新版本

### 安装步骤

1. **克隆项目**
```bash
git clone https://github.com/your-username/Wechat-Auto.git
cd Wechat-Auto
```

2. **安装依赖**
```bash
pip install -r requirements.txt
```

3. **运行程序**
```bash
python main.py
```

### 打包为exe程序

如果您想将程序打包为独立的exe文件，无需Python环境即可运行：

**一键打包**
```bash
# Windows下双击运行
build.bat
```

打包后将生成完全独立的exe程序，包含所有依赖，可在任何Windows 10/11电脑上运行。

详细的打包说明请查看 [BUILD.md](BUILD.md) 文档。

### 首次使用

1. **配置微信路径**
   - 打开设置 → 微信设置
   - 设置微信程序路径（通常在 `C:\Program Files\Tencent\WeChat\WeChat.exe`）
   - 可使用"自动检测"功能

2. **准备账号数据**
   - 在 `resource/accounts.csv` 文件中添加要关注的公众号ID
   - 格式：每行一个公众号ID

3. **开始使用**
   - 批量关注：使用"批量关注"功能
   - 数据采集：使用"批量采集"功能
   - 数据查看：使用"数据查看"功能

## 📖 使用指南

### 账号管理
- **添加账号**: 在账号管理页面输入公众号ID并点击添加
- **批量导入**: 支持从CSV文件批量导入账号
- **编辑删除**: 支持单个账号的编辑和删除操作

### 批量关注
- **选择账号**: 从账号列表中选择要关注的公众号
- **设置参数**: 配置关注间隔和其他参数
- **开始关注**: 点击开始按钮执行批量关注

### 数据采集
- **采集设置**: 配置最大采集数量和停止条件
- **开始采集**: 自动采集公众号信息和文章数据
- **进度监控**: 实时查看采集进度和状态

### 数据查看
- **数据统计**: 查看数据概览和统计信息
- **搜索筛选**: 支持关键词搜索和列筛选
- **数据导出**: 支持导出为CSV格式

## ⚙️ 配置说明

### 微信设置
- **微信路径**: 微信程序的安装路径
- **加载延迟**: 微信启动后的等待时间
- **最大化窗口**: 是否最大化微信窗口

### 采集设置
- **最大采集数量**: 单次采集的最大文章数量
- **停止存在计数**: 遇到已存在文章时的停止阈值
- **账号文件**: 账号数据文件路径
- **信息文件**: 公众号信息文件路径

## 🔧 开发说明

### 技术栈
- **GUI框架**: PySide6 (Qt6)
- **自动化**: Windows API + UI自动化
- **数据处理**: Pandas, JSON
- **配置管理**: JSON配置文件

### 代码结构
- **MVC架构**: 分离界面、逻辑和数据
- **模块化设计**: 功能模块独立，便于维护
- **配置驱动**: 通过配置文件控制程序行为

### 扩展开发
- **添加新功能**: 在对应模块中添加新的服务类
- **自定义界面**: 在ui模块中创建新的界面组件
- **数据处理**: 在core模块中扩展数据处理逻辑

## ⚠️ 注意事项

### 使用须知
- **合规使用**: 请遵守微信使用条款，避免频繁操作
- **数据备份**: 建议定期备份重要数据
- **版本兼容**: 确保微信PC版为最新版本

### 常见问题
- **微信检测失败**: 检查微信是否正确安装和路径配置
- **自动化失败**: 确保微信窗口可见且未被其他程序遮挡
- **数据丢失**: 检查文件权限和磁盘空间

## 🤝 贡献指南

欢迎提交Issue和Pull Request来帮助改进项目！

### 贡献方式
1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📞 联系方式

如有问题或建议，请通过以下方式联系：

- 📧 Email: <EMAIL>
- 🐛 Issues: [GitHub Issues](https://github.com/your-username/Wechat-Auto/issues)

---

<div align="center">

**⭐ 如果这个项目对您有帮助，请给它一个星标！**

Made with ❤️ by [Your Name]

</div>
