aiofiles==24.1.0
aiohappyeyeballs==2.6.1
aiohttp==3.11.18
aiosignal==1.3.2
aiosqlite==0.21.0
albucore==0.0.24
albumentations==2.0.7
altgraph==0.17.4
annotated-types==0.7.0
anyio @ file:///C:/b/abs_5c8w79vk0u/croot/anyio_1745334672105/work
argon2-cffi @ file:///opt/conda/conda-bld/argon2-cffi_1645000214183/work
argon2-cffi-bindings @ file:///C:/b/abs_f11axiliot/croot/argon2-cffi-bindings_1736182463870/work
astor==0.8.1
asttokens @ file:///C:/b/abs_9662ywy9fp/croot/asttokens_1743630464377/work
async-lru @ file:///C:/Users/<USER>/perseverance-python-buildout/croot/async-lru_1701796859357/work
attrs @ file:///C:/b/abs_89hmquz5ga/croot/attrs_1734533130810/work
babel @ file:///C:/b/abs_ffzt1bmjth/croot/babel_1737454394148/work
beautifulsoup4 @ file:///C:/b/abs_d5wytg_p0w/croot/beautifulsoup4-split_1718029833749/work
bleach @ file:///C:/b/abs_925i9psm3u/croot/bleach_1732292896852/work
blinker==1.9.0
Brotli @ file:///C:/b/abs_c415aux9ra/croot/brotli-split_1736182803933/work
bs4==0.0.2
cbor2==5.6.5
cchardet==2.2.0a2
certifi @ file:///C:/b/abs_3beajm7umk/croot/certifi_1745939228545/work/certifi
cffi @ file:///C:/b/abs_29_b57if3f/croot/cffi_1736184144340/work
chardet==5.2.0
charset-normalizer @ file:///croot/charset-normalizer_1721748349566/work
click==8.1.8
colorama @ file:///C:/Users/<USER>/perseverance-python-buildout/croot/colorama_1699472650914/work
coloredlogs==15.0.1
colorlog==6.9.0
colorthief==0.2.1
comm @ file:///C:/b/abs_67a8058udb/croot/comm_1709322909844/work
comtypes==1.4.10
configparser==7.2.0
cryptography==44.0.3
cssselect==1.3.0
Cython==3.1.1
darkdetect==0.8.0
DataRecorder==3.6.2
dateparser==1.2.1
ddddocr==1.5.6
debugpy @ file:///C:/b/abs_bf9oo2vhxp/croot/debugpy_1736269476451/work
decorator @ file:///opt/conda/conda-bld/decorator_1643638310831/work
defusedxml @ file:///tmp/build/80754af9/defusedxml_1615228127516/work
Distance==0.1.3
distro==1.9.0
django-crawler==0.1
docopt==0.6.2
docx2pdf==0.1.8
DownloadKit==2.0.7
DrissionPage==4.1.0.18
environs==14.1.1
et_xmlfile==2.0.0
executing @ file:///opt/conda/conda-bld/executing_1646925071911/work
fake-http-header==0.3.5
fake-useragent==2.2.0
fastjsonschema @ file:///C:/b/abs_4ev90296ly/croot/python-fastjsonschema_1731939386416/work
filelock==3.18.0
fire==0.7.0
Flask==3.1.1
flatbuffers==25.2.10
fonttools==4.58.0
frozenlist==1.6.0
fsspec==2025.3.2
gerapy-auto-extractor==0.2.1
greenlet==3.2.2
h11 @ file:///C:/b/abs_1czwoyexjf/croot/h11_1706652332846/work
html2text==2025.4.15
html_text==0.7.0
httpcore @ file:///C:/b/abs_55n7g233bw/croot/httpcore_1706728507241/work
httpx @ file:///C:/b/abs_9fclaf1b46/croot/httpx_1746747856672/work
huggingface-hub==0.31.1
humanfriendly==10.0
humanize==4.12.3
idna==3.10
imageio==2.37.0
imgtool==2.1.0
importlib_metadata==8.7.0
imutils==0.5.4
intelhex==2.3.0
ipykernel @ file:///C:/b/abs_6c9ggygp01/croot/ipykernel_1737660720620/work
ipython @ file:///C:/b/abs_307wfft5yu/croot/ipython_1744912982439/work
ipython_pygments_lexers @ file:///C:/b/abs_b66hj1lo19/croot/ipython_pygments_lexers_1744753262043/work
itsdangerous==2.2.0
jedi @ file:///C:/b/abs_3a2kbnlclc/croot/jedi_1733987412687/work
Jinja2 @ file:///C:/b/abs_920kup4e6u/croot/jinja2_1741711580669/work
jiojio==1.2.7
jionlp==1.5.22
jiter==0.9.0
joblib==1.5.0
json5 @ file:///C:/b/abs_743lprxrv5/croot/json5_1730786818336/work
jsonschema @ file:///C:/b/abs_394_t6__xq/croot/jsonschema_1728486718320/work
jsonschema-specifications @ file:///C:/Users/<USER>/py312/jsonschema-specifications_1706803038066/work
jupyter-events @ file:///C:/b/abs_9cm3qlticu/croot/jupyter_events_1741184612840/work
jupyter-lsp @ file:///C:/b/abs_7171flzdkg/croot/jupyter-lsp-meta_1745827033118/work
jupyter_client @ file:///C:/b/abs_149bw133if/croot/jupyter_client_1737570986926/work
jupyter_core @ file:///C:/b/abs_beftpbuevw/croot/jupyter_core_1718818307097/work
jupyter_server @ file:///C:/b/abs_dd442s1uya/croot/jupyter_server_1741206396661/work
jupyter_server_terminals @ file:///C:/b/abs_adjrm9dtns/croot/jupyter_server_terminals_1744706714294/work
jupyterlab @ file:///C:/b/abs_adgv7wgabm/croot/jupyterlab_1737555444636/work
jupyterlab_pygments @ file:///C:/b/abs_d5alfet8m6/croot/jupyterlab_pygments_1741124274578/work
jupyterlab_server @ file:///C:/b/abs_fdi5r_tpjc/croot/jupyterlab_server_1725865372811/work
keyboard==0.13.5
lark-parser==0.12.0
lazy_loader==0.4
litellm==1.69.1
lmdb==1.6.2
loguru==0.7.3
lxml==5.1.1
lxml_html_clean==0.4.2
magic_html @ https://github.com/opendatalab/magic-html/releases/download/magic_html-0.1.5-released/magic_html-0.1.5-py3-none-any.whl#sha256=405ee00a98a3e889e65af0b8fbc9ea0840439ad97a9d747b06ea3c30c6906c77
markdown-it-py==3.0.0
markdownify==1.1.0
MarkupSafe @ file:///C:/b/abs_a0ma7ge0jc/croot/markupsafe_1738584052792/work
marshmallow==4.0.0
matplotlib-inline @ file:///C:/Users/<USER>/perseverance-python-buildout/croot/matplotlib-inline_1699484796387/work
mdurl==0.1.2
mistune @ file:///C:/b/abs_77yql3poyz/croot/mistune_1741124004410/work
MouseInfo==0.1.3
mpmath==1.3.0
multidict==6.4.3
nbclient @ file:///C:/b/abs_a09c4t3h8x/croot/nbclient_1741124030330/work
nbconvert @ file:///C:/b/abs_c27_60dzt8/croot/nbconvert-meta_1741191385337/work
nbformat @ file:///C:/b/abs_c2jkw46etm/croot/nbformat_1728050303821/work
nest-asyncio @ file:///C:/b/abs_65d6lblmoi/croot/nest-asyncio_1708532721305/work
networkx==3.4.2
nltk==3.9.1
notebook @ file:///C:/b/abs_b552cuftgu/croot/notebook_1738159967315/work
notebook_shim @ file:///C:/b/abs_9ctyfgpncn/croot/notebook-shim_1741707829491/work
Nuitka==2.7.12
numpy==2.2.5
onnxruntime==1.22.0
openai==1.75.0
opencv-contrib-python==4.11.0.86
opencv-python==4.11.0.86
opencv-python-headless==4.11.0.86
openpyxl==3.1.5
opt-einsum==3.3.0
ordered-set==4.1.0
outcome==1.3.0.post0
overrides @ file:///C:/Users/<USER>/perseverance-python-buildout/croot/overrides_1701806336503/work
packaging @ file:///C:/b/abs_3by6s2fa66/croot/packaging_1734472138782/work
paddleocr==2.10.0
pandas==2.2.3
pandocfilters @ file:///opt/conda/conda-bld/pandocfilters_1643405455980/work
parso @ file:///C:/b/abs_834b4mj92b/croot/parso_1733963322289/work
pefile==2023.2.7
Pillow==9.5.0
platformdirs @ file:///C:/b/abs_ddh15014or/croot/platformdirs_1744273060660/work
playwright==1.52.0
plum-dispatch==1.7.4
prometheus_client @ file:///C:/b/abs_8b175q_ub8/croot/prometheus_client_1744271638821/work
prompt-toolkit @ file:///C:/b/abs_68uwr58ed1/croot/prompt-toolkit_1704404394082/work
propcache==0.3.1
protobuf==6.31.0
psutil==6.1.0
pure-eval @ file:///opt/conda/conda-bld/pure_eval_1646925070566/work
py-asciimath==0.3.0
PyAutoGUI==0.9.54
pycaw==20240210
pyclipper==1.3.0.post6
pycparser @ file:///tmp/build/80754af9/pycparser_1636541352034/work
pycryptodome @ file:///C:/b/abs_79emxjlcaj/croot/pycryptodome_1737042221951/work
pydantic==2.11.4
pydantic_core==2.33.2
pyee==13.0.0
PyGetWindow==0.0.9
Pygments @ file:///C:/b/abs_e4bg5vh5j_/croot/pygments_1744667628203/work
pyinstaller==6.13.0
pyinstaller-hooks-contrib==2025.4
Pymem==1.13.0
PyMsgBox==1.0.9
pynput==1.7.7
pyOpenSSL==25.0.0
PyPDF2==3.0.1
pyperclip==1.9.0
pyreadline3==3.5.4
PyRect==0.2.0
PyScreeze==1.0.1
PySide6==6.9.0
PySide6-Fluent-Widgets==1.8.1
PySide6_Addons==6.9.0
PySide6_Essentials==6.9.0
PySideSix-Frameless-Window==0.7.3
PySocks @ file:///C:/Users/<USER>/perseverance-python-buildout/croot/pysocks_1699473336188/work
pytesseract==0.3.7
python-dateutil @ file:///C:/b/abs_3au_koqnbs/croot/python-dateutil_1716495777160/work
python-docx==1.1.2
python-dotenv==1.1.0
python-json-logger @ file:///C:/b/abs_0cm_mnox0z/croot/python-json-logger_1734370042436/work
pytweening==1.2.0
pytz==2025.2
pywin32==308
pywin32-ctypes==0.2.3
pywinauto==0.6.9
pywinpty @ file:///C:/b/abs_883wh7sts8/croot/pywinpty_1741871674963/work
PyYAML @ file:///C:/b/abs_14xkfs39bx/croot/pyyaml_1728657968772/work
pyzmq @ file:///C:/b/abs_f3yte6j5yn/croot/pyzmq_1734711069724/work
rank-bm25==0.2.2
RapidFuzz==3.13.0
readability-lxml==*******
referencing @ file:///C:/Users/<USER>/py312/referencing_1706802962559/work
regex==2024.11.6
requests @ file:///C:/b/abs_c3508vg8ez/croot/requests_1731000584867/work
requests-file==2.1.0
rfc3339-validator @ file:///C:/Users/<USER>/perseverance-python-buildout/croot/rfc3339-validator_1699543924991/work
rfc3986-validator @ file:///C:/Users/<USER>/perseverance-python-buildout/croot/rfc3986-validator_1699543955651/work
rich==14.0.0
rpds-py @ file:///C:/b/abs_0c6z5kcdb6/croot/rpds-py_1736545465023/work
scikit-image==0.25.2
scikit-learn==1.6.1
scipy==1.15.3
selenium==4.33.0
Send2Trash @ file:///C:/b/abs_7e73ol18dl/croot/send2trash_1736542724140/work
setuptools==80.7.1
shapely==2.1.1
shiboken6==6.9.0
simsimd==6.2.1
six @ file:///C:/b/abs_149wuyuo1o/croot/six_1744271521515/work
sniffio @ file:///C:/b/abs_3akdewudo_/croot/sniffio_1705431337396/work
snowballstemmer==2.2.0
sortedcontainers==2.4.0
soupsieve @ file:///C:/Users/<USER>/perseverance-python-buildout/croot/soupsieve_1699496611169/work
spire==0.4.2
Spire.Pdf==10.12.1
stack-data @ file:///opt/conda/conda-bld/stack_data_1646927590127/work
stringzilla==3.12.5
sympy==1.14.0
tabulate==0.9.0
termcolor==3.1.0
terminado @ file:///C:/Users/<USER>/perseverance-python-buildout/croot/terminado_1699545066607/work
tf-playwright-stealth==1.1.2
threadpoolctl==3.6.0
tifffile==2025.5.10
tiktoken==0.9.0
tinycss2 @ file:///C:/b/abs_df38owi5ma/croot/tinycss2_1738337725183/work
tldextract==5.2.0
tokenizers==0.21.1
tornado @ file:///C:/b/abs_7cyu943ybx/croot/tornado_1733960510898/work
tqdm==4.67.1
traitlets @ file:///C:/b/abs_bfsnoxl4pq/croot/traitlets_1718227069245/work
trio==0.30.0
trio-websocket==0.12.2
typing-inspection==0.4.0
typing_extensions==4.13.2
tzdata==2025.2
tzlocal==5.3.1
uiautomation==2.0.20
urllib3==2.4.0
wcwidth @ file:///Users/<USER>/demo/mc3/conda-bld/wcwidth_1629357192024/work
webdriver-manager==4.0.2
webencodings @ file:///C:/Users/<USER>/perseverance-python-buildout/croot/webencodings_1699497069416/work
websocket-client @ file:///C:/b/abs_5dmnxxoci9/croot/websocket-client_1715878351319/work
Werkzeug==3.1.3
wheel==0.45.1
win-inet-pton @ file:///C:/Users/<USER>/perseverance-python-buildout/croot/win_inet_pton_1699472992992/work
win32_setctime==1.2.0
winshell==0.6
wsproto==1.2.0
xxhash==3.5.0
yarl==1.20.0
zipfile36==0.1.3
zipp==3.21.0
zstandard==0.23.0
