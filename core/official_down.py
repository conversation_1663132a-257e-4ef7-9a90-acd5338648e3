import logging
import os
import re
import time

import html2text
import requests
from PySide6.QtCore import QObject, Signal
from bs4 import BeautifulSoup


class WeChatArticleCrawler(QObject):
    h = html2text.HTML2Text()
    log_message = Signal(str, str)  # 消息, 级别
    def __init__(self, /, output_dir='downloads'):
        super().__init__()
        self.output_dir = output_dir
        os.makedirs(self.output_dir, exist_ok=True)
        # 初始化html2text转换器
        self.h.ignore_links = False
        self.h.bypass_tables = False

        # 请求头
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Referer': 'https://mp.weixin.qq.com/'
        }

    def get_safe_title(self, title):
        safe_title = re.sub(r'[\\/*?:"<>|]', '', title)[:50]
        return safe_title.strip()

    # 下载并保存微信公众号图片到文章对应的图片目录
    def download_wechat_image(self, img_url, article_image_dir):
        try:
            # 微信图片特殊处理
            if 'mmbiz.qpic.cn' in img_url:
                if not img_url.startswith(('http://', 'https://')):
                    img_url = 'https://' + img_url

                # 提取图片格式参数
                fmt_match = re.search(r'wx_fmt=([^&]+)', img_url)
                fmt = fmt_match.group(1) if fmt_match else 'jpeg'

                # 构造高质量图片URL
                if '/0?' in img_url:
                    img_url = img_url.replace('/0?', '/640?')
                elif '?' not in img_url:
                    img_url += '?wx_fmt=' + fmt

                # 添加时间戳防止缓存
                img_url += f'×tamp={int(time.time())}'

            headers = self.headers.copy()
            response = requests.get(img_url, headers=headers, stream=True, timeout=15)

            if response.status_code == 200:
                # 确定文件扩展名
                content_type = response.headers.get('Content-Type', '')
                if 'jpeg' in content_type or 'jpg' in content_type:
                    ext = '.jpg'
                elif 'png' in content_type:
                    ext = '.png'
                elif 'gif' in content_type:
                    ext = '.gif'
                else:
                    ext = '.jpg'  # 默认

                # 生成图片文件名
                img_name = f'{int(time.time() * 1000)}{ext}'
                img_path = os.path.join(article_image_dir, img_name)

                with open(img_path, 'wb') as f:
                    for chunk in response.iter_content(1024):
                        f.write(chunk)

                return img_name  # 只返回文件名，不包含路径

        except Exception as e:
            # 下载微信图片失败
            self.log_message.emit(f"Failed to download WeChat image: {img_url}, Error: {str(e)[:100]}", "ERROR")
        return None

    def extract_real_image_url(self, img_element):
        # 尝试多种可能的属性
        for attr in ['data-src', 'src', 'data-original', 'data-wx-src']:
            img_url = img_element.get(attr)
            if img_url:
                # 处理微信的图片URL
                if 'mmbiz.qpic.cn' in img_url:
                    if not img_url.startswith(('http://', 'https://')):
                        img_url = 'https://' + img_url
                    return img_url
                elif img_url.startswith('//'):
                    return 'https:' + img_url
                elif img_url.startswith('/'):
                    return 'https://mp.weixin.qq.com' + img_url
                else:
                    return img_url
        return None

    def create_enhanced_html(self, content_div, title, info, url):
        """创建包含本地图片链接的增强HTML页面"""
        # 创建新的HTML文档结构
        html_doc = BeautifulSoup('<!DOCTYPE html><html><head></head><body></body></html>', 'html.parser')

        # 添加meta标签和标题
        meta_charset = html_doc.new_tag('meta')
        meta_charset['charset'] = 'utf-8'
        html_doc.head.append(meta_charset)

        meta_viewport = html_doc.new_tag('meta')
        meta_viewport['name'] = 'viewport'
        meta_viewport['content'] = 'width=device-width, initial-scale=1.0'
        html_doc.head.append(meta_viewport)

        title_tag = html_doc.new_tag('title')
        title_tag.string = title
        html_doc.head.append(title_tag)

        # 添加CSS样式
        style_tag = html_doc.new_tag('style')
        style_tag.string = """
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #fff;
        }
        .article-header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 1px solid #eee;
        }
        .article-title {
            font-size: 28px;
            font-weight: bold;
            margin-bottom: 10px;
            color: #2c3e50;
        }
        .article-date {
            color: #7f8c8d;
            font-size: 14px;
        }
        .article-content {
            font-size: 16px;
            line-height: 1.8;
        }
        .article-content img {
            max-width: 100%;
            height: auto;
            display: block;
            margin: 20px auto;
            border-radius: 4px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .article-content p {
            margin-bottom: 16px;
        }
        .article-footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #eee;
            text-align: center;
            color: #7f8c8d;
            font-size: 12px;
        }
        """
        html_doc.head.append(style_tag)

        # 创建文章头部
        header_div = html_doc.new_tag('div')
        header_div['class'] = 'article-header'

        title_h1 = html_doc.new_tag('h1')
        title_h1['class'] = 'article-title'
        title_h1.string = title
        header_div.append(title_h1)

        date_div = html_doc.new_tag('div')
        date_div['class'] = 'article_info'
        date_div.string = f'{info}'
        header_div.append(date_div)

        html_doc.body.append(header_div)

        # 添加文章内容
        content_wrapper = html_doc.new_tag('div')
        content_wrapper['class'] = 'article-content'
        content_wrapper.append(content_div)
        html_doc.body.append(content_wrapper)

        # 添加页脚
        footer_div = html_doc.new_tag('div')
        footer_div['class'] = 'article-footer'
        footer_div.string = f'原文链接: {url}'  # Original link: {url}
        html_doc.body.append(footer_div)

        return str(html_doc)

    def process_article(self, url, official_account=None, save_to_downloads=True):
        # 处理单篇文章
        try:
            # 检查URL是否为空或无效
            if not url or not url.startswith('http'):
                # 跳过无效的URL
                self.log_message.emit(f"Skipping invalid URL: {url}", "WARNING")
                return None

            # 开始处理文章
            self.log_message.emit(f"Start processing article: {url}", "INFO")

            # 获取文章HTML
            response = requests.get(url, headers=self.headers)
            response.encoding = 'utf-8'
            if response.status_code != 200:
                # 无法获取文章
                self.log_message.emit(f"Failed to get article: {url}, Status code: {response.status_code}", "ERROR")
                return None

            soup = BeautifulSoup(response.text, 'html.parser')

            # 提取文章标题
            title = soup.find('h1', {'id': 'activity-name'})
            title = title.get_text().strip() if title else 'Untitled'  # 无标题
            safe_title = self.get_safe_title(title)
            # 文章标题
            self.log_message.emit(f"Article title: {title}", "INFO")

            # 提取信息项
            info = soup.find('div', id='meta_content').get_text().strip()

            # 尝试从JavaScript变量中提取创建时间
            create_time = None
            scripts = soup.find_all('script')
            for script in scripts:
                if script.string and 'var createTime' in script.string:
                    # 使用正则表达式提取日期
                    match = re.search(r"var createTime\s*=\s*'([^']+)'", script.string)
                    if match:
                        create_time = match.group(1)
                        break

            # 如果找到了JavaScript中的创建时间，则使用它
            if create_time:
                info = f"{create_time}"

            # # 提取发布日期
            # date = soup.find('div', id='meta_content')
            # date = date.get_text().strip() if date else datetime.now().strftime('%Y-%m-%d')

            # 只在downloads模式下保存
            if not (save_to_downloads and official_account):
                return {
                    'success': True,
                    'message': 'Save files only in downloads mode'  # 仅在downloads模式下保存文件
                }

            # 保存到downloads目录，按公众号分类
            base_output_dir = f"downloads/{official_account}"
            os.makedirs(base_output_dir, exist_ok=True)

            # 创建文章对应的图片目录
            article_image_dir = os.path.join(base_output_dir, 'images', safe_title)
            os.makedirs(article_image_dir, exist_ok=True)

            # 处理图片
            content_div = soup.find('div', {'id': 'js_content'})
            if not content_div:
                # 未找到文章内容
                self.log_message.emit(f"Article content not found: {url}", "WARNING")
                return None

            # 移除content_div的隐藏属性
            del content_div.attrs['style']

            # 下载并替换图片链接
            img_count = 0
            max_images = 30  # 限制最大图片下载数量
            for img in content_div.find_all('img'):
                if img_count >= max_images:
                    # 已达到最大图片下载数量限制
                    self.log_message.emit(f"Maximum image download limit ({max_images}) reached, skipping remaining images", "INFO")
                    break

                img_url = self.extract_real_image_url(img)
                if img_url:
                    # 发现图片
                    self.log_message.emit(f"Found image: {img_url}", "INFO")
                    try:
                        img_filename = self.download_wechat_image(img_url, article_image_dir)
                        if img_filename:
                            img_count += 1
                            # 在Markdown中使用相对路径
                            img['src'] = f'images/{safe_title}/{img_filename}'  # 修改为相对路径
                            # 图片下载成功
                            self.log_message.emit(f"Image downloaded successfully: {img_filename}", "INFO")
                        else:
                            # 图片下载失败
                            self.log_message.emit("Image download failed", "WARNING")
                    except Exception as e:
                        # 图片下载异常
                        self.log_message.emit(f"Image download exception: {str(e)[:100]}", "ERROR")
                        continue
                else:
                    # 未找到有效的图片URL
                    self.log_message.emit("No valid image URL found", "WARNING")

            # 共处理 X 张图片
            self.log_message.emit(f"Processed {img_count} images in total", "INFO")

            # 转换为Markdown格式
            html_content = str(content_div)
            markdown_content = self.h.handle(html_content)

            # 添加标题和日期
            # markdown_content = f"# {title}\n\n发布时间: {date}\n\n{markdown_content}"
            markdown_content = f"# {title}\n\n {info}\n\n{markdown_content}"

            # downloads模式：根据配置保存格式，添加时间戳
            from datetime import datetime
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            base_filename = f"{safe_title}_{timestamp}"

            # 获取下载格式配置
            try:
                from wechat.auto_process.officical_process import DOWNLOAD_FORMAT
                download_format = DOWNLOAD_FORMAT
            except ImportError:
                download_format = "MD + HTML"

            if "MD" in download_format:
                article_filename = f"{base_filename}.md"
                article_path = os.path.join(base_output_dir, article_filename)

                with open(article_path, 'w', encoding='utf-8') as f:
                    f.write(markdown_content)
                # 成功保存Markdown
                self.log_message.emit(f"Markdown saved successfully: {article_path}", "INFO")

            if "HTML" in download_format:
                enhanced_html_filename = f"{base_filename}.html"
                enhanced_html_path = os.path.join(base_output_dir, enhanced_html_filename)

                # 创建增强的HTML内容
                enhanced_html_content = self.create_enhanced_html(content_div, title, info, url)

                # 构建downloads专用的HTML内容
                downloads_html_content = f"""<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{title}</title>
    <style>
        body {{
            font-family: 'Microsoft YaHei', sans-serif;
            line-height: 1.8;
            margin: 40px;
            background-color: #f8f9fa;
            color: #333;
        }}
        .container {{
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 40px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }}
        h1 {{
            color: #2c3e50;
            border-bottom: 3px solid #4CAF50;
            padding-bottom: 15px;
            margin-bottom: 30px;
        }}
        .meta {{
            color: #666;
            font-size: 14px;
            margin-bottom: 30px;
            padding: 20px;
            background-color: #f8f9fa;
            border-left: 4px solid #4CAF50;
            border-radius: 4px;
        }}
        .content {{ margin-top: 30px; }}
        a {{ color: #4CAF50; text-decoration: none; }}
        a:hover {{ text-decoration: underline; }}
        img {{ max-width: 100%; height: auto; margin: 10px 0; }}
    </style>
</head>
<body>
    <div class="container">
        <div class="content">
            {enhanced_html_content}
        </div>
    </div>
</body>
</html>"""
                with open(enhanced_html_path, 'w', encoding='utf-8') as f:
                    f.write(downloads_html_content)
                # 成功保存HTML
                self.log_message.emit(f"HTML saved successfully: {enhanced_html_path}", "INFO")
            # 返回详细的文章信息
            return {
                'success': True
            }
        except Exception as e:
            import traceback
            # 处理文章时出错
            self.log_message.emit(f"Error processing article: {traceback.format_exc()}", "ERROR")
            return {
                'success': False,
                'error': str(e)
            }