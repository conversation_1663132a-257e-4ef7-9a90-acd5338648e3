"""
公众号信息管理服务
"""
import json
import os
from typing import List, Dict, Optional
from config.settings import settings


class OfficialInfo:
    """公众号信息类"""
    
    def __init__(self, official_account: str, official_name: str):
        self.official_account = official_account
        self.official_name = official_name
    
    def to_dict(self) -> Dict[str, str]:
        """转换为字典"""
        return {
            "official_account": self.official_account,
            "official_name": self.official_name
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, str]) -> 'OfficialInfo':
        """从字典创建实例"""
        return cls(
            official_account=data.get("official_account", ""),
            official_name=data.get("official_name", "")
        )


class OfficialService:
    """公众号信息管理服务类"""
    
    def __init__(self):
        self.official_infos_file = settings.get('data.official_infos_file', 'official_infos.json')
    
    def load_official_infos(self) -> List[OfficialInfo]:
        """加载公众号信息列表"""
        official_infos = []
        if os.path.exists(self.official_infos_file):
            try:
                with open(self.official_infos_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    if isinstance(data, list):
                        official_infos = [OfficialInfo.from_dict(item) for item in data]
            except Exception as e:
                print(f"加载公众号信息文件失败: {e}")
        return official_infos
    
    def save_official_infos(self, official_infos: List[OfficialInfo]):
        """保存公众号信息列表"""
        try:
            data = [info.to_dict() for info in official_infos]
            with open(self.official_infos_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=4)
        except Exception as e:
            print(f"保存公众号信息文件失败: {e}")
            raise
    
    def add_official_info(self, official_account: str, official_name: str) -> bool:
        """添加公众号信息"""
        if not official_account.strip() or not official_name.strip():
            return False
        
        official_infos = self.load_official_infos()
        
        # 检查是否已存在
        for info in official_infos:
            if info.official_account == official_account.strip():
                return False
        
        new_info = OfficialInfo(official_account.strip(), official_name.strip())
        official_infos.append(new_info)
        self.save_official_infos(official_infos)
        return True
    
    def remove_official_info(self, official_account: str) -> bool:
        """删除公众号信息"""
        official_infos = self.load_official_infos()
        for i, info in enumerate(official_infos):
            if info.official_account == official_account:
                official_infos.pop(i)
                self.save_official_infos(official_infos)
                return True
        return False
    
    def update_official_info(self, official_account: str, new_name: str) -> bool:
        """更新公众号信息"""
        if not new_name.strip():
            return False
        
        official_infos = self.load_official_infos()
        for info in official_infos:
            if info.official_account == official_account:
                info.official_name = new_name.strip()
                self.save_official_infos(official_infos)
                return True
        return False
    
    def get_official_info(self, official_account: str) -> Optional[OfficialInfo]:
        """获取指定公众号信息"""
        official_infos = self.load_official_infos()
        for info in official_infos:
            if info.official_account == official_account:
                return info
        return None
    
    def get_official_count(self) -> int:
        """获取公众号数量"""
        return len(self.load_official_infos())
    
    def batch_add_from_follow_result(self, follow_results: List[Dict[str, str]]):
        """从关注结果批量添加公众号信息"""
        official_infos = self.load_official_infos()
        existing_accounts = {info.official_account for info in official_infos}
        
        new_infos = []
        for result in follow_results:
            if result.get('success') and result.get('official_account'):
                account = result['official_account']
                name = result.get('official_name', account)
                
                if account not in existing_accounts:
                    new_infos.append(OfficialInfo(account, name))
                    existing_accounts.add(account)
        
        if new_infos:
            official_infos.extend(new_infos)
            self.save_official_infos(official_infos)
