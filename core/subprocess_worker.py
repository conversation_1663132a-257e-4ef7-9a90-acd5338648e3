#!/usr/bin/env python3
"""
子进程工作脚本
用于在独立进程中执行关注和采集任务
"""
import logging
import sys
import json
import os
import traceback
from pathlib import Path

from wechat.auto_process.officical_process import OfficialProcess
# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('wechat_auto.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
official_process = OfficialProcess()
def send_message(msg_type, message, progress=None):
    logging.info(f"发送消息: {msg_type} - {message} - {progress}")
    """发送消息到主进程"""
    msg = {
        'type': msg_type,
        'message': message
    }
    if progress is not None:
        msg['progress'] = progress

    try:
        # 尝试使用UTF-8编码发送消息
        output = json.dumps(msg, ensure_ascii=False, separators=(',', ':')) + '\n'
        sys.stdout.buffer.write(output.encode('utf-8'))
        sys.stdout.flush()
    except Exception as e:
        # 如果编码失败，使用默认方式并记录错误
        try:
            print(json.dumps(msg, ensure_ascii=True))
            sys.stdout.flush()
        except:
            pass  # 忽略输出错误


def execute_follow_task(config):
    """执行关注任务"""
    try:
        # 初始化COM
        from core.com_init import initialize_com
        if not initialize_com():
            send_message('log', "COM初始化失败")
            return False

        send_message('log', "COM初始化成功")

        # 设置微信路径
        official_process.WECHAT_PATH = config['wechat_path']

        accounts = config['accounts']
        total = len(accounts)

        send_message('log', f"开始关注任务，共 {total} 个账号")

        success_count = 0
        follow_results = []  # 保存关注结果

        for i, account in enumerate(accounts):
            try:
                send_message('progress', f"正在关注: {account}", int((i / total) * 100))
                send_message('log', f"正在关注公众号: {account}")
                result = official_process.follow_official(accounts=[account])
                if result and len(result) > 0:
                    # result是一个列表，包含关注结果字典
                    for follow_result in result:
                        if follow_result:
                            success_count += 1
                            follow_results.append(follow_result)
                            official_name = follow_result.get('official_name', account)
                            send_message('log', f"关注成功: {account} -> {official_name}")
                        else:
                            send_message('log', f"关注失败: {account}")
                else:
                    send_message('log', f"关注失败: {account}")

            except Exception as e:
                send_message('log', f"关注异常 {account}: {str(e)}")

        # 保存关注结果到临时文件
        if follow_results:
            try:
                import json
                results_file = os.path.join(os.getcwd(), 'temp_follow_results.json')
                with open(results_file, 'w', encoding='utf-8') as f:
                    json.dump(follow_results, f, ensure_ascii=False, indent=2)
                send_message('log', f"关注结果已保存到临时文件: {len(follow_results)} 个")
            except Exception as e:
                send_message('log', f"保存关注结果失败: {str(e)}")

        send_message('progress', "关注任务完成", 100)
        send_message('log', f"关注任务完成，成功关注 {success_count} 个账号")
        send_message('log', "开始清理资源...")
        return True

    except Exception as e:
        send_message('log', f"关注任务异常: {str(e)}")
        traceback.print_exc()
        return False
    finally:
        # 清理COM
        try:
            send_message('log', "正在清理COM资源...")
            from core.com_init import cleanup_com
            cleanup_com()
            send_message('log', "COM资源清理完成")
        except Exception as e:
            send_message('log', f"COM清理异常: {str(e)}")

        # 强制刷新输出
        try:
            sys.stdout.flush()
            sys.stderr.flush()
        except:
            pass

        send_message('log', "子进程即将退出...")


def execute_collect_task(config):
    """执行采集任务"""
    try:
        # 初始化COM
        from core.com_init import initialize_com
        if not initialize_com():
            send_message('log', "COM初始化失败")
            return False

        send_message('log', "COM初始化成功")

        # 设置微信路径
        official_process.WECHAT_PATH = config['wechat_path']

        official_infos = config['official_infos']
        limit_count = config['limit_count']
        stop_exist_count = config['stop_exist_count']
        total = len(official_infos)

        send_message('log', f"开始采集任务，共 {total} 个公众号")
        send_message('log', f"采集参数: limit_count={limit_count}, stop_exist_count={stop_exist_count}")
        for i, official in enumerate(official_infos):
            try:
                official_name = official['official_name']
                official_account = official['official_account']

                send_message('progress', f"正在采集: {official_name}", int((i / total) * 100))
                send_message('log', f"正在采集公众号: {official_name} ({official_account})")

                official_process.collect_official(
                    [official],
                    limit_count=limit_count,
                    stop_exist_count=stop_exist_count
                )

                send_message('log', f"采集完成: {official_name}")

            except Exception as e:
                import traceback
                traceback.print_exc()
                send_message('log', f"采集异常 {official.get('official_name', 'Unknown')}: {str(e)}")

        send_message('progress', "采集任务完成", 100)
        send_message('log', f"采集任务完成，共处理 {total} 个公众号")
        send_message('log', "开始清理资源...")
        send_message('log', "子进程即将退出...")
        return True

    except Exception as e:
        send_message('log', f"采集任务异常: {str(e)}")
        return False



def main():
    """主函数"""
    if len(sys.argv) != 2:
        print("Usage: subprocess_worker.py <config_file>")
        sys.exit(1)

    config_file = sys.argv[1]
    success = False

    try:
        send_message('log', f"子进程启动，配置文件: {config_file}")

        # 读取配置文件
        with open(config_file, 'r', encoding='utf-8') as f:
            config = json.load(f)

        task_type = config.get('task_type')
        send_message('log', f"任务类型: {task_type}")

        if task_type == 'follow':
            execute_follow_task(config)
        elif task_type == 'collect':
            execute_collect_task(config)
        else:
            send_message('log', f"未知的任务类型: {task_type}")
            success = False

        send_message('log', f"任务执行完成，结果: {'成功' if success else '失败'}")

    except Exception as e:
        send_message('log', f"子进程执行异常: {str(e)}")
        traceback.print_exc()
        success = False

    finally:
        # 强制刷新所有输出
        try:
            sys.stdout.flush()
            sys.stderr.flush()
        except:
            pass

        # 清理配置文件
        try:
            if os.path.exists(config_file):
                os.remove(config_file)
                send_message('log', f"已清理配置文件: {config_file}")
        except Exception as e:
            send_message('log', f"清理配置文件失败: {str(e)}")

        send_message('log', "子进程正在退出...")

        # 强制退出
        exit_code = 0 if success else 1
        send_message('log', f"退出代码: {exit_code}")

        # 最后一次刷新输出
        try:
            sys.stdout.flush()
            sys.stderr.flush()
        except:
            pass

        # 强制退出进程
        os._exit(exit_code)


if __name__ == '__main__':
    main()
