"""
账号管理服务
"""
import csv
import os
from typing import List, Optional
from config.settings import settings


class AccountService:
    """账号管理服务类"""
    
    def __init__(self):
        self.accounts_file = settings.get('data.accounts_file', 'accounts.csv')
    
    def load_accounts(self) -> List[str]:
        """加载账号列表"""
        accounts = []
        if os.path.exists(self.accounts_file):
            try:
                with open(self.accounts_file, 'r', encoding='utf-8') as f:
                    accounts = [line.strip() for line in f.readlines() if line.strip()]
            except Exception as e:
                print(f"加载账号文件失败: {e}")
        return accounts
    
    def save_accounts(self, accounts: List[str]):
        """保存账号列表"""
        try:
            with open(self.accounts_file, 'w', encoding='utf-8') as f:
                for account in accounts:
                    if account.strip():  # 只保存非空账号
                        f.write(f"{account.strip()}\n")
        except Exception as e:
            print(f"保存账号文件失败: {e}")
            raise
    
    def add_account(self, account: str) -> bool:
        """添加账号"""
        if not account.strip():
            return False
        
        accounts = self.load_accounts()
        if account.strip() not in accounts:
            accounts.append(account.strip())
            self.save_accounts(accounts)
            return True
        return False
    
    def remove_account(self, account: str) -> bool:
        """删除账号"""
        accounts = self.load_accounts()
        if account in accounts:
            accounts.remove(account)
            self.save_accounts(accounts)
            return True
        return False
    
    def update_account(self, old_account: str, new_account: str) -> bool:
        """更新账号"""
        if not new_account.strip():
            return False
        
        accounts = self.load_accounts()
        if old_account in accounts:
            index = accounts.index(old_account)
            accounts[index] = new_account.strip()
            self.save_accounts(accounts)
            return True
        return False
    
    def get_account_count(self) -> int:
        """获取账号数量"""
        return len(self.load_accounts())
    
    def validate_account(self, account: str) -> bool:
        """验证账号格式"""
        # 简单的账号格式验证
        if not account or not account.strip():
            return False
        
        account = account.strip()
        # 微信公众号ID通常是字母、数字、下划线的组合
        if len(account) < 3 or len(account) > 50:
            return False
        
        # 可以添加更多验证规则
        return True
