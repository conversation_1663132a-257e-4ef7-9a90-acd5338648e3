"""
微信路径自动检测模块
"""
import os
import winreg
import psutil
from pathlib import Path
from typing import Optional, List
import logging


class WechatPathDetector:
    """微信路径检测器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def detect_wechat_path(self) -> Optional[str]:
        """
        自动检测微信安装路径
        按优先级顺序尝试不同的检测方法
        """
        detection_methods = [
            self._detect_from_running_process,
            self._detect_from_registry,
            self._detect_from_common_paths,
            self._detect_from_start_menu,
            self._detect_from_desktop
        ]
        
        for method in detection_methods:
            try:
                path = method()
                if path and self._validate_wechat_path(path):
                    self.logger.info(f"通过 {method.__name__} 找到微信路径: {path}")
                    return path
            except Exception as e:
                self.logger.debug(f"{method.__name__} 检测失败: {e}")
        
        self.logger.warning("未能自动检测到微信路径")
        return None
    
    def _detect_from_running_process(self) -> Optional[str]:
        """从正在运行的进程中检测微信路径"""
        try:
            for proc in psutil.process_iter(['pid', 'name', 'exe']):
                if proc.info['name'] and proc.info['name'].lower() in ['wechat.exe', 'weixin.exe']:
                    exe_path = proc.info['exe']
                    if exe_path and os.path.exists(exe_path):
                        return exe_path
        except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
            pass
        return None
    
    def _detect_from_registry(self) -> Optional[str]:
        """从注册表检测微信路径"""
        registry_paths = [
            (winreg.HKEY_CURRENT_USER, r"Software\Tencent\WeChat", "InstallPath"),
            (winreg.HKEY_LOCAL_MACHINE, r"SOFTWARE\Tencent\WeChat", "InstallPath"),
            (winreg.HKEY_LOCAL_MACHINE, r"SOFTWARE\WOW6432Node\Tencent\WeChat", "InstallPath"),
            (winreg.HKEY_CURRENT_USER, r"Software\Microsoft\Windows\CurrentVersion\Uninstall\WeChat", "InstallLocation"),
            (winreg.HKEY_LOCAL_MACHINE, r"SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\WeChat", "InstallLocation"),
        ]
        
        for hkey, subkey, value_name in registry_paths:
            try:
                with winreg.OpenKey(hkey, subkey) as key:
                    install_path = winreg.QueryValueEx(key, value_name)[0]
                    if install_path:
                        # 尝试不同的可执行文件名
                        for exe_name in ['WeChat.exe', 'Weixin.exe']:
                            exe_path = os.path.join(install_path, exe_name)
                            if os.path.exists(exe_path):
                                return exe_path
            except (FileNotFoundError, OSError, winreg.error):
                continue
        
        return None
    
    def _detect_from_common_paths(self) -> Optional[str]:
        """从常见安装路径检测微信"""
        common_paths = [
            # 默认安装路径
            r"C:\Program Files\Tencent\WeChat\WeChat.exe",
            r"C:\Program Files (x86)\Tencent\WeChat\WeChat.exe",
            r"D:\Program Files\Tencent\WeChat\WeChat.exe",
            r"D:\Program Files (x86)\Tencent\WeChat\WeChat.exe",
            r"E:\Program Files\Tencent\WeChat\WeChat.exe",
            r"E:\Program Files (x86)\Tencent\WeChat\WeChat.exe",
            
            # 用户自定义路径
            r"C:\Tencent\WeChat\WeChat.exe",
            r"D:\Tencent\WeChat\WeChat.exe",
            r"E:\Tencent\WeChat\WeChat.exe",
            
            # 绿色版路径
            r"C:\WeChat\WeChat.exe",
            r"D:\WeChat\WeChat.exe",
            r"E:\WeChat\WeChat.exe",
            
            # 其他可能的路径
            r"C:\Program Files\WeChat\WeChat.exe",
            r"C:\Program Files (x86)\WeChat\WeChat.exe",
            r"D:\Program Files\WeChat\WeChat.exe",
            r"D:\Program Files (x86)\WeChat\WeChat.exe",
        ]
        
        for path in common_paths:
            if os.path.exists(path):
                return path
        
        return None
    
    def _detect_from_start_menu(self) -> Optional[str]:
        """从开始菜单快捷方式检测微信路径"""
        start_menu_paths = [
            os.path.expandvars(r"%APPDATA%\Microsoft\Windows\Start Menu\Programs"),
            os.path.expandvars(r"%PROGRAMDATA%\Microsoft\Windows\Start Menu\Programs"),
        ]
        
        for start_path in start_menu_paths:
            if not os.path.exists(start_path):
                continue
            
            # 搜索微信相关的快捷方式
            for root, dirs, files in os.walk(start_path):
                for file in files:
                    if file.lower() in ['微信.lnk', 'wechat.lnk', 'weixin.lnk']:
                        lnk_path = os.path.join(root, file)
                        target_path = self._resolve_shortcut(lnk_path)
                        if target_path and self._validate_wechat_path(target_path):
                            return target_path
        
        return None
    
    def _detect_from_desktop(self) -> Optional[str]:
        """从桌面快捷方式检测微信路径"""
        desktop_paths = [
            os.path.join(os.path.expanduser("~"), "Desktop"),
            os.path.join(os.path.expandvars("%PUBLIC%"), "Desktop"),
        ]
        
        for desktop_path in desktop_paths:
            if not os.path.exists(desktop_path):
                continue
            
            for file in os.listdir(desktop_path):
                if file.lower() in ['微信.lnk', 'wechat.lnk', 'weixin.lnk']:
                    lnk_path = os.path.join(desktop_path, file)
                    target_path = self._resolve_shortcut(lnk_path)
                    if target_path and self._validate_wechat_path(target_path):
                        return target_path
        
        return None
    
    def _resolve_shortcut(self, lnk_path: str) -> Optional[str]:
        """解析快捷方式获取目标路径"""
        try:
            import win32com.client
            shell = win32com.client.Dispatch("WScript.Shell")
            shortcut = shell.CreateShortCut(lnk_path)
            return shortcut.Targetpath
        except ImportError:
            # 如果没有win32com，尝试使用其他方法
            return None
        except Exception:
            return None
    
    def _validate_wechat_path(self, path: str) -> bool:
        """验证路径是否为有效的微信程序"""
        if not path or not os.path.exists(path):
            return False
        
        if not path.lower().endswith('.exe'):
            return False
        
        # 检查文件名是否包含微信相关关键词
        filename = os.path.basename(path).lower()
        if not any(keyword in filename for keyword in ['wechat', 'weixin']):
            return False
        
        # 检查文件大小（微信程序通常比较大）
        try:
            file_size = os.path.getsize(path)
            if file_size < 1024 * 1024:  # 小于1MB的文件可能不是微信程序
                return False
        except OSError:
            return False
        
        return True
    
    def get_all_possible_paths(self) -> List[str]:
        """获取所有可能的微信路径（用于用户选择）"""
        paths = []
        
        # 从各种方法收集路径
        detection_methods = [
            self._detect_from_running_process,
            self._detect_from_registry,
            self._detect_from_common_paths,
            self._detect_from_start_menu,
            self._detect_from_desktop
        ]
        
        for method in detection_methods:
            try:
                path = method()
                if path and path not in paths and self._validate_wechat_path(path):
                    paths.append(path)
            except Exception:
                continue
        
        return paths


# 全局检测器实例
wechat_detector = WechatPathDetector()
