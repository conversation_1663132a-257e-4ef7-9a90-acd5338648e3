"""
定时任务调度器
支持Cron表达式的定时任务调度
"""
import re
import time
import threading
from datetime import datetime, timedelta
from typing import Callable, Optional, List
from PySide6.QtCore import QObject, Signal, QTimer


class CronExpression:
    """Cron表达式解析器"""
    
    def __init__(self, expression: str):
        self.expression = expression.strip()
        self.fields = self._parse_expression()
    
    def _parse_expression(self) -> List[List[int]]:
        """解析Cron表达式"""
        parts = self.expression.split()
        if len(parts) != 5:
            raise ValueError("Cron表达式必须包含5个字段：分 时 日 月 周")
        
        fields = []
        ranges = [(0, 59), (0, 23), (1, 31), (1, 12), (0, 7)]  # 分 时 日 月 周
        
        for i, (part, (min_val, max_val)) in enumerate(zip(parts, ranges)):
            field_values = self._parse_field(part, min_val, max_val)
            fields.append(field_values)
        
        return fields
    
    def _parse_field(self, field: str, min_val: int, max_val: int) -> List[int]:
        """解析单个字段"""
        if field == '*':
            return list(range(min_val, max_val + 1))
        
        values = []
        for part in field.split(','):
            if '/' in part:
                # 处理步长，如 */2
                range_part, step = part.split('/')
                step = int(step)
                if range_part == '*':
                    values.extend(range(min_val, max_val + 1, step))
                else:
                    start, end = map(int, range_part.split('-'))
                    values.extend(range(start, end + 1, step))
            elif '-' in part:
                # 处理范围，如 1-5
                start, end = map(int, part.split('-'))
                values.extend(range(start, end + 1))
            else:
                # 单个值
                values.append(int(part))
        
        return sorted(list(set(values)))
    
    def matches(self, dt: datetime) -> bool:
        """检查给定时间是否匹配Cron表达式"""
        minute, hour, day, month, weekday = self.fields
        
        # 转换周日为0（Python的weekday()返回0-6，周一为0）
        dt_weekday = dt.weekday()
        if dt_weekday == 6:  # 周日
            dt_weekday = 0
        else:
            dt_weekday += 1
        
        return (
            dt.minute in minute and
            dt.hour in hour and
            dt.day in day and
            dt.month in month and
            (dt_weekday in weekday or 7 in weekday)  # 7也表示周日
        )
    
    def next_run_time(self, from_time: Optional[datetime] = None) -> datetime:
        """计算下次运行时间"""
        if from_time is None:
            from_time = datetime.now()
        
        # 从下一分钟开始检查
        next_time = from_time.replace(second=0, microsecond=0) + timedelta(minutes=1)
        
        # 最多检查一年
        max_time = from_time + timedelta(days=366)
        
        while next_time < max_time:
            if self.matches(next_time):
                return next_time
            next_time += timedelta(minutes=1)
        
        raise ValueError("无法在一年内找到下次运行时间")


class TaskScheduler(QObject):
    """任务调度器"""
    
    task_triggered = Signal(str)  # 任务触发信号
    
    def __init__(self):
        super().__init__()
        self.tasks = {}  # task_id -> (cron_expression, callback)
        self.running = False
        self.timer = QTimer()
        self.timer.timeout.connect(self._check_tasks)
        self.timer.setInterval(60000)  # 每分钟检查一次
    
    def add_task(self, task_id: str, cron_expression: str, callback: Callable = None):
        """添加定时任务"""
        try:
            cron = CronExpression(cron_expression)
            self.tasks[task_id] = {
                'cron': cron,
                'callback': callback,
                'expression': cron_expression,
                'next_run': cron.next_run_time(),
                'last_run': None
            }
            print(f"添加定时任务: {task_id}, 表达式: {cron_expression}")
            print(f"下次运行时间: {self.tasks[task_id]['next_run']}")
            return True
        except Exception as e:
            print(f"添加定时任务失败: {e}")
            return False
    
    def remove_task(self, task_id: str):
        """移除定时任务"""
        if task_id in self.tasks:
            del self.tasks[task_id]
            print(f"移除定时任务: {task_id}")
    
    def start(self):
        """启动调度器"""
        if not self.running:
            self.running = True
            self.timer.start()
            print("定时任务调度器已启动")
    
    def stop(self):
        """停止调度器"""
        if self.running:
            self.running = False
            self.timer.stop()
            print("定时任务调度器已停止")
    
    def _check_tasks(self):
        """检查并执行到期的任务"""
        current_time = datetime.now().replace(second=0, microsecond=0)
        
        for task_id, task_info in self.tasks.items():
            next_run = task_info['next_run']
            
            if current_time >= next_run:
                print(f"执行定时任务: {task_id}")
                
                # 更新最后运行时间
                task_info['last_run'] = current_time
                
                # 计算下次运行时间
                try:
                    task_info['next_run'] = task_info['cron'].next_run_time(current_time)
                    print(f"下次运行时间: {task_info['next_run']}")
                except Exception as e:
                    print(f"计算下次运行时间失败: {e}")
                
                # 触发任务
                if task_info['callback']:
                    try:
                        task_info['callback']()
                    except Exception as e:
                        print(f"执行任务回调失败: {e}")
                
                # 发送信号
                self.task_triggered.emit(task_id)
    
    def get_task_info(self, task_id: str) -> dict:
        """获取任务信息"""
        if task_id in self.tasks:
            task = self.tasks[task_id]
            return {
                'expression': task['expression'],
                'next_run': task['next_run'],
                'last_run': task['last_run']
            }
        return None
    
    def list_tasks(self) -> dict:
        """列出所有任务"""
        result = {}
        for task_id, task_info in self.tasks.items():
            result[task_id] = {
                'expression': task_info['expression'],
                'next_run': task_info['next_run'],
                'last_run': task_info['last_run']
            }
        return result


# 全局调度器实例
_scheduler = None

def get_scheduler() -> TaskScheduler:
    """获取全局调度器实例"""
    global _scheduler
    if _scheduler is None:
        _scheduler = TaskScheduler()
    return _scheduler


def validate_cron_expression(expression: str) -> tuple[bool, str]:
    """验证Cron表达式"""
    try:
        cron = CronExpression(expression)
        next_run = cron.next_run_time()
        return True, f"有效的Cron表达式，下次运行时间: {next_run.strftime('%Y-%m-%d %H:%M')}"
    except Exception as e:
        return False, f"无效的Cron表达式: {str(e)}"
