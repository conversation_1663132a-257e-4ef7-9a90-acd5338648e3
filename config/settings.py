"""
应用程序配置管理
"""
import os
import json
from typing import Dict, Any, Optional


class Settings:
    """应用程序设置管理类"""
    
    def __init__(self):
        self.config_file = "resource/config.json"
        self.default_config = {
            "wechat": {
                "wechat_path": "",  # 微信路径
                "load_delay": 3,    # 加载延迟
                "is_maximize": False  # 是否最大化窗口
            },
            "task": {
                "limit_count": 200,      # 最大采集数量
                "stop_exist_count": 30   # 停止存在计数
            },
            "ui": {
                "window_width": 800,  # 默认最小宽度
                "window_height": 600,  # 默认最小高度
                "theme": "light",
                "auto_size": True  # 自动根据屏幕大小调整
            },
            "data": {
                "accounts_file": "resource/accounts.csv",
                "official_infos_file": "resource/official_infos.json"
            }
        }
        self.config = self.load_config()
    
    def load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                # 合并默认配置，确保所有必要的键都存在
                return self._merge_config(self.default_config, config)
            except Exception as e:
                print(f"加载配置文件失败: {e}")
                return self.default_config.copy()
        else:
            return self.default_config.copy()
    
    def save_config(self):
        """保存配置文件"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, ensure_ascii=False, indent=4)
        except Exception as e:
            print(f"保存配置文件失败: {e}")
    
    def _merge_config(self, default: Dict, user: Dict) -> Dict:
        """合并配置，确保所有默认键都存在"""
        result = default.copy()
        for key, value in user.items():
            if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                result[key] = self._merge_config(result[key], value)
            else:
                result[key] = value
        return result
    
    def get(self, key_path: str, default=None):
        """获取配置值，支持点分隔的路径"""
        keys = key_path.split('.')
        value = self.config
        for key in keys:
            if isinstance(value, dict) and key in value:
                value = value[key]
            else:
                return default
        return value

    def get_int(self, key_path: str, default: int = 0) -> int:
        """安全地获取整数配置值，确保返回值不为None"""
        value = self.get(key_path, default)
        if value is None:
            return default
        try:
            return int(value)
        except (ValueError, TypeError):
            return default

    def get_str(self, key_path: str, default: str = '') -> str:
        """安全地获取字符串配置值，确保返回值不为None"""
        value = self.get(key_path, default)
        if value is None:
            return default
        return str(value)

    def get_bool(self, key_path: str, default: bool = False) -> bool:
        """安全地获取布尔配置值，确保返回值不为None"""
        value = self.get(key_path, default)
        if value is None:
            return default
        return bool(value)
    
    def set(self, key_path: str, value):
        """设置配置值，支持点分隔的路径"""
        keys = key_path.split('.')
        config = self.config
        for key in keys[:-1]:
            if key not in config:
                config[key] = {}
            config = config[key]
        config[keys[-1]] = value
        self.save_config()

    def get_wechat_path(self) -> Optional[str]:
        """获取微信路径，如果未设置则自动检测"""
        wechat_path = self.get('wechat.wechat_path')

        # 如果已配置且文件存在，直接返回
        if wechat_path and os.path.exists(wechat_path):
            return wechat_path

        # 自动检测微信路径
        try:
            from core.wechat_detector import wechat_detector
            detected_path = wechat_detector.detect_wechat_path()

            if detected_path:
                # 保存检测到的路径
                self.set('wechat.wechat_path', detected_path)
                print(f"自动检测到微信路径: {detected_path}")
                return detected_path
        except Exception as e:
            print(f"自动检测微信路径失败: {e}")

        return None

    def auto_detect_and_save_wechat_path(self) -> bool:
        """自动检测并保存微信路径"""
        try:
            from core.wechat_detector import wechat_detector
            detected_path = wechat_detector.detect_wechat_path()

            if detected_path:
                self.set('wechat.wechat_path', detected_path)
                return True
        except Exception as e:
            print(f"自动检测微信路径失败: {e}")

        return False


# 全局设置实例
settings = Settings()
