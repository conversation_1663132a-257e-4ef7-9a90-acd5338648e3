"""
关注管理界面
"""
import logging

from PySide6.QtCore import Qt, Signal
from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QPushButton,
                               QListWidget, QListWidgetItem, QLabel, QProgressBar,
                               QCheckBox, QGroupBox, QSplitter)

from core.account_service import AccountService
from core.official_service import OfficialService
from core.task_manager import TaskManager
from ui.components.log_widget import LogWidget
from ui.message_box import CustomMessageBox


class FollowManager(QWidget):
    """关注管理界面"""

    log_message = Signal(str, str)  # 消息, 级别

    def __init__(self, parent=None):
        super().__init__(parent)
        self.account_service = AccountService()
        self.official_service = OfficialService()
        self.task_manager = TaskManager()
        self.current_task = None
        self.setup_ui()
        self.load_accounts()

    def setup_ui(self):
        """设置UI"""
        layout = QVBoxLayout(self)
        layout.setSpacing(8)
        layout.setContentsMargins(10, 10, 10, 10)

        # 创建左右布局的主容器
        main_splitter = QSplitter(Qt.Orientation.Horizontal)
        main_splitter.setStyleSheet("""
            QSplitter::handle {
                background-color: #e0e0e0;
                width: 2px;
            }
        """)

        # 左侧：账号选择和操作区域
        left_widget = QWidget()
        left_layout = QVBoxLayout(left_widget)
        left_layout.setContentsMargins(0, 0, 5, 0)

        # 关注操作组件
        operation_group = QGroupBox("关注操作")
        operation_group.setMaximumHeight(80)
        operation_layout = QVBoxLayout(operation_group)
        operation_layout.setContentsMargins(8, 8, 8, 8)
        operation_layout.setSpacing(8)

        # 将所有控制组件放在同一行
        control_layout = QHBoxLayout()
        control_layout.setSpacing(8)

        self.start_btn = QPushButton("开始关注")
        self.start_btn.clicked.connect(self.start_follow)
        self.start_btn.setStyleSheet("""
                    QPushButton {
                        background-color: #4CAF50;
                        color: white;
                        border: none;
                        padding: 8px 16px;
                        font-weight: 500;
                        font-size: 12px;
                        min-width: 30px;
                        min-height: 23px;
                        border-radius: 4px;
                    }
                    QPushButton:hover {
                        background-color: #45a049;
                    }
                """)
        control_layout.addWidget(self.start_btn)

        self.stop_btn = QPushButton("停止关注")
        self.stop_btn.clicked.connect(self.stop_follow)
        self.stop_btn.setEnabled(False)
        self.stop_btn.setStyleSheet("""
                    QPushButton {
                        background-color: #F44336;
                        color: white;
                        border: none;
                        padding: 8px 16px;
                        font-weight: 500;
                        font-size: 12px;
                        min-width: 30px;
                        min-height: 23px;
                        border-radius: 4px;
                    }
                    QPushButton:hover {
                        background-color: #D32F2F;
                    }
                    QPushButton:disabled {
                        background-color: #BDBDBD;
                    }
                """)
        control_layout.addWidget(self.stop_btn)

        # 进度显示组件
        progress_label = QLabel("进度:")
        progress_label.setStyleSheet("font-size: 11px; color: #333333;")
        control_layout.addWidget(progress_label)

        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        self.progress_bar.setMaximumHeight(20)
        self.progress_bar.setStyleSheet("""
                    QProgressBar {
                        border: 1px solid #e0e0e0;
                        text-align: center;
                        font-size: 10px;
                        font-weight: 400;
                    }
                    QProgressBar::chunk {
                        background-color: #4CAF50;
                    }
                """)
        control_layout.addWidget(self.progress_bar)

        self.progress_label = QLabel("")
        self.progress_label.setStyleSheet("font-size: 11px; color: #666666;")
        control_layout.addWidget(self.progress_label)

        control_layout.addStretch()
        operation_layout.addLayout(control_layout)

        left_layout.addWidget(operation_group)

        # 账号选择组
        account_group = QGroupBox("选择要关注的账号")
        account_layout = QVBoxLayout(account_group)
        account_layout.setSpacing(6)

        # 全选/取消全选工具栏
        select_layout = QHBoxLayout()
        select_layout.setSpacing(6)

        button_style = """
            QPushButton {
                background-color: #2196F3;
                color: white;
                border: none;
                padding: 6px 12px;
                font-weight: 500;
                font-size: 11px;
                min-width: 60px;
                min-height: 24px;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #1976D2;
            }
        """

        self.select_all_btn = QPushButton("全选")
        self.select_all_btn.clicked.connect(self.select_all_accounts)
        self.select_all_btn.setStyleSheet(button_style)
        select_layout.addWidget(self.select_all_btn)

        self.deselect_all_btn = QPushButton("取消全选")
        self.deselect_all_btn.clicked.connect(self.deselect_all_accounts)
        self.deselect_all_btn.setStyleSheet(button_style)
        select_layout.addWidget(self.deselect_all_btn)

        self.refresh_btn = QPushButton("刷新列表")
        self.refresh_btn.clicked.connect(self.load_accounts)
        self.refresh_btn.setStyleSheet("""
            QPushButton {
                background-color: #FF9800;
                color: white;
                border: none;
                padding: 6px 12px;
                font-weight: 500;
                font-size: 11px;
                min-width: 60px;
                min-height: 24px;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #F57C00;
            }
        """)
        select_layout.addWidget(self.refresh_btn)

        select_layout.addStretch()
        account_layout.addLayout(select_layout)

        # 账号列表
        self.account_list = QListWidget()
        self.account_list.setStyleSheet("""
            QListWidget {
                font-size: 11px;
                min-height: 300px;
            }
            QListWidget::item {
                padding: 3px 6px;
                margin: 1px;
            }
        """)
        account_layout.addWidget(self.account_list)
        left_layout.addWidget(account_group)

        main_splitter.addWidget(left_widget)

        # 右侧：操作日志区域
        right_widget = QWidget()
        right_layout = QVBoxLayout(right_widget)
        right_layout.setContentsMargins(5, 0, 0, 0)

        log_group = QGroupBox("操作日志")
        log_layout = QVBoxLayout(log_group)
        log_layout.setContentsMargins(6, 6, 6, 6)

        self.log_widget = LogWidget()
        self.log_widget.setStyleSheet("""
            QTextEdit {
                font-size: 12px;
                min-height: 400px;
            }
        """)
        log_layout.addWidget(self.log_widget)

        right_layout.addWidget(log_group)
        main_splitter.addWidget(right_widget)

        # 设置分割器比例 (左:右 = 1:1)
        main_splitter.setSizes([400, 400])

        layout.addWidget(main_splitter)

        # 连接日志信号
        self.log_message.connect(self.log_widget.add_log)

    def load_accounts(self):
        """加载账号列表"""
        self.account_list.clear()
        accounts = self.account_service.load_accounts()

        for account in accounts:
            item = QListWidgetItem()
            checkbox = QCheckBox(account)
            self.account_list.addItem(item)
            self.account_list.setItemWidget(item, checkbox)

        self.log_message.emit(f"加载了 {len(accounts)} 个账号", "INFO")

    def select_all_accounts(self):
        """全选账号"""
        for i in range(self.account_list.count()):
            item = self.account_list.item(i)
            checkbox = self.account_list.itemWidget(item)
            if checkbox:
                checkbox.setChecked(True)

    def deselect_all_accounts(self):
        """取消全选账号"""
        for i in range(self.account_list.count()):
            item = self.account_list.item(i)
            checkbox = self.account_list.itemWidget(item)
            if checkbox:
                checkbox.setChecked(False)

    def get_selected_accounts(self):
        """获取选中的账号"""
        selected_accounts = []
        for i in range(self.account_list.count()):
            item = self.account_list.item(i)
            checkbox = self.account_list.itemWidget(item)
            if checkbox and checkbox.isChecked():
                selected_accounts.append(checkbox.text())
        return selected_accounts

    def start_follow(self):
        """开始关注"""
        selected_accounts = self.get_selected_accounts()

        if not selected_accounts:
            CustomMessageBox.warning(self, "警告", "请选择要关注的账号")
            return

        if self.task_manager.is_task_running():
            CustomMessageBox.warning(self, "警告", "已有任务正在运行")
            return

        # 确认对话框
        reply = CustomMessageBox.question(
            self, "确认关注",
            f"确定要关注 {len(selected_accounts)} 个公众号吗？"
        )

        if reply != CustomMessageBox.Yes:
            return

        try:
            # 启动关注任务
            self.current_task = self.task_manager.start_follow_task(selected_accounts)

            # 连接信号
            self.current_task.signals.progress.connect(self.update_progress)
            self.current_task.signals.finished.connect(self.follow_finished)
            self.current_task.signals.log.connect(lambda msg: self.log_message.emit(msg, "INFO"))

            # 更新UI状态
            self.start_btn.setEnabled(False)
            self.stop_btn.setEnabled(True)
            self.progress_bar.setVisible(True)
            self.progress_bar.setValue(0)

            self.log_message.emit(f"开始关注任务，共 {len(selected_accounts)} 个账号", "INFO")

        except Exception as e:
            CustomMessageBox.critical(self, "错误", f"启动关注任务失败: {str(e)}")
            self.log_message.emit(f"启动关注任务失败: {str(e)}", "ERROR")

    def stop_follow(self):
        """停止关注"""
        if self.current_task:
            reply = CustomMessageBox.question(
                self, "确认停止", "确定要停止关注任务吗？"
            )

            if reply == CustomMessageBox.Yes:
                self.task_manager.cancel_current_task()
                self.log_message.emit("用户取消了关注任务", "INFO")

    def update_progress(self, value: int, message: str):
        """更新进度"""
        self.progress_bar.setValue(value)
        self.progress_label.setText(message)

    def follow_finished(self, success: bool, message: str):
        """关注任务完成"""
        # 首先清理任务状态
        self.current_task = None

        # 确保任务管理器状态也被清理
        if hasattr(self.task_manager, 'current_task'):
            self.task_manager.current_task = None

        # 更新UI状态
        self.start_btn.setEnabled(True)
        self.stop_btn.setEnabled(False)
        self.progress_bar.setVisible(False)
        self.progress_label.setText("")

        # 显示结果
        if success:
            self.log_message.emit(message, "INFO")

            # 关注成功后，将数据写入official_infos.json
            self.update_official_infos_after_follow()

            CustomMessageBox.information(self, "任务完成", message)
        else:
            self.log_message.emit(message, "ERROR")
            CustomMessageBox.warning(self, "任务失败", message)

        print(f"[DEBUG] 关注任务完成，任务状态已清理，成功: {success}")

    def update_official_infos_after_follow(self):
        """关注成功后更新official_infos.json"""
        try:
            import json
            import os
            from datetime import datetime

            # 首先尝试从临时文件读取关注结果
            follow_results = []
            results_file = os.path.join(os.getcwd(), 'temp_follow_results.json')

            if os.path.exists(results_file):
                try:
                    with open(results_file, 'r', encoding='utf-8') as f:
                        follow_results = json.load(f)
                    self.log_message.emit(f"从临时文件读取到 {len(follow_results)} 个关注结果", "INFO")

                    # 读取后删除临时文件
                    os.remove(results_file)
                    self.log_message.emit("已清理临时结果文件", "INFO")

                except Exception as e:
                    self.log_message.emit(f"读取关注结果文件失败: {str(e)}", "WARNING")
                    follow_results = []

            # 如果没有关注结果，使用选中的账号作为备选
            if not follow_results:
                selected_accounts = self.get_selected_accounts()
                if not selected_accounts:
                    return
                # 构建简单的结果结构
                follow_results = [
                    {"official_account": account, "official_name": account}
                    for account in selected_accounts
                ]

            # 读取现有的official_infos.json
            official_infos_file = os.path.join(os.getcwd(), 'resource', 'official_infos.json')

            # 确保data目录存在
            os.makedirs(os.path.dirname(official_infos_file), exist_ok=True)

            existing_infos = []
            if os.path.exists(official_infos_file):
                try:
                    with open(official_infos_file, 'r', encoding='utf-8') as f:
                        existing_infos = json.load(f)
                except Exception as e:
                    self.log_message.emit(f"读取official_infos.json失败: {str(e)}", "WARNING")
                    existing_infos = []

            # 获取已存在的公众号账号列表
            existing_accounts = {info.get('official_account', '') for info in existing_infos}

            # 添加新关注的公众号信息
            new_infos_added = 0
            current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

            for result in follow_results:
                account = result.get('official_account', '')
                official_name = result.get('official_name', '')

                if account and account not in existing_accounts and official_name != '':
                    new_info = {
                        "official_name": official_name,  # 使用真实的公众号名称
                        "official_account": account,
                        "description": f"通过批量关注添加于 {current_time}",
                        "follow_date": current_time,
                        "status": "已关注"
                    }
                    existing_infos.append(new_info)
                    new_infos_added += 1

                    self.log_message.emit(f"添加公众号信息: {account} -> {official_name}", "INFO")

            # 写入更新后的数据
            if new_infos_added > 0:
                with open(official_infos_file, 'w', encoding='utf-8') as f:
                    json.dump(existing_infos, f, ensure_ascii=False, indent=2)

                self.log_message.emit(f"已将 {new_infos_added} 个新关注的公众号添加到official_infos.json", "INFO")
                print(f"[DEBUG] 已添加 {new_infos_added} 个公众号信息到official_infos.json")
            else:
                self.log_message.emit("所有关注的公众号信息已存在，无需更新", "INFO")

        except Exception as e:
            error_msg = f"更新official_infos.json失败: {str(e)}"
            self.log_message.emit(error_msg, "ERROR")
            print(f"[DEBUG] {error_msg}")
            import traceback
            traceback.print_exc()

            # 异常情况下也要清理临时文件
            try:
                results_file = os.path.join(os.getcwd(), 'temp_follow_results.json')
                if os.path.exists(results_file):
                    os.remove(results_file)
                    self.log_message.emit("已清理临时结果文件", "INFO")
            except Exception as cleanup_e:
                print(f"[DEBUG] 清理临时文件失败: {cleanup_e}")
