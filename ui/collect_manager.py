"""
采集管理界面
"""

from PySide6.QtCore import Qt, Signal, QTimer
from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QPushButton,
                               QListWidget, QListWidgetItem, QLabel, QProgressBar,
                               QCheckBox, QGroupBox,
                               QSplitter, QComboBox, QLineEdit)

from config.settings import settings
from core.official_service import OfficialService
from core.scheduler import get_scheduler, validate_cron_expression
from core.task_manager import TaskManager
from ui.components.log_widget import LogWidget
from ui.message_box import CustomMessageBox
from wechat.auto_process.officical_process import OfficialProcess

official_process = OfficialProcess()

class CollectManager(QWidget):
    """采集管理界面"""
    log_message = Signal(str, str)  # 消息, 级别

    def __init__(self, parent=None):
        super().__init__(parent)
        self.official_service = OfficialService()
        self.task_manager = TaskManager()
        self.current_task = None
        self.setup_ui()
        self.load_official_infos()

    def setup_ui(self):
        """设置UI"""
        layout = QVBoxLayout(self)
        layout.setSpacing(8)
        layout.setContentsMargins(10, 10, 10, 10)

        # 创建左右布局的主容器
        main_splitter = QSplitter(Qt.Orientation.Horizontal)
        main_splitter.setStyleSheet("""
            QSplitter::handle {
                background-color: #e0e0e0;
                width: 3px;
                border-radius: 1px;
            }
            QSplitter::handle:hover {
                background-color: #2196F3;
            }
        """)

        # 左侧：主要操作区域
        left_widget = QWidget()
        left_layout = QVBoxLayout(left_widget)
        left_layout.setContentsMargins(0, 0, 8, 0)
        left_layout.setSpacing(8)

        official_group = QGroupBox("选择要采集的公众号")
        official_group.setStyleSheet("""
            QGroupBox {
                font-size: 13px;
                font-weight: 600;
                color: #333;
                border: 2px solid #e0e0e0;
                border-radius: 6px;
                padding-top: 12px;
                margin-top: 6px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 8px 0 8px;
                background-color: white;
            }
        """)
        official_layout = QVBoxLayout(official_group)
        official_layout.setContentsMargins(8, 8, 8, 8)
        official_layout.setSpacing(6)

        # 全选/取消全选工具栏
        select_layout = QHBoxLayout()
        select_layout.setSpacing(6)

        button_style = """
            QPushButton {
                background-color: #2196F3;
                color: white;
                border: none;
                padding: 4px 8px;
                font-weight: 400;
                font-size: 10px;
                min-width: 50px;
                border-radius: 2px;
            }
            QPushButton:hover {
                background-color: #1976D2;
            }
        """

        self.select_all_btn = QPushButton("全选")
        self.select_all_btn.clicked.connect(self.select_all_officials)
        self.select_all_btn.setStyleSheet(button_style)
        select_layout.addWidget(self.select_all_btn)

        self.deselect_all_btn = QPushButton("取消全选")
        self.deselect_all_btn.clicked.connect(self.deselect_all_officials)
        self.deselect_all_btn.setStyleSheet(button_style)
        select_layout.addWidget(self.deselect_all_btn)

        self.refresh_btn = QPushButton("刷新列表")
        self.refresh_btn.clicked.connect(self.load_official_infos)
        self.refresh_btn.setStyleSheet("""
            QPushButton {
                background-color: #FF9800;
                color: white;
                border: none;
                padding: 4px 8px;
                font-weight: 400;
                font-size: 10px;
                min-width: 50px;
            }
            QPushButton:hover {
                background-color: #F57C00;
            }
        """)
        select_layout.addWidget(self.refresh_btn)

        select_layout.addStretch()
        official_layout.addLayout(select_layout)

        # 公众号列表
        self.official_list = QListWidget()
        self.official_list.setStyleSheet("""
            QListWidget {
                font-size: 12px;
                min-height: 300px;
                border: 1px solid #e0e0e0;
                background-color: white;
                outline: none;
            }
            QListWidget::item {
                padding: 8px 12px;
                margin: 1px;
            }
        """)
        official_layout.addWidget(self.official_list)

        # 先添加工具栏到左侧布局（稍后会移动到这里）
        # left_layout.addLayout(toolbar_layout) - 将在后面添加

        # 然后添加公众号选择组
        left_layout.addWidget(official_group)
        main_splitter.addWidget(left_widget)

        # 顶部工具栏 - 分为三行：第一行基础参数，第二行定时任务，第三行操作控制
        toolbar_layout = QVBoxLayout()
        toolbar_layout.setSpacing(4)

        # 第一行：基础采集参数行
        params_row_layout = QHBoxLayout()
        params_row_layout.setSpacing(8)

        # 基础采集参数组
        params_group = QGroupBox("基础参数")
        params_group.setMaximumHeight(60)
        params_group.setMinimumWidth(250)
        params_layout = QHBoxLayout(params_group)
        params_layout.setContentsMargins(8, 5, 8, 5)
        params_layout.setSpacing(10)

        # 最大采集数量
        params_layout.addWidget(QLabel("最大数量:"))
        self.limit_count_edit = QLineEdit()
        self.limit_count_edit.setText(str(settings.get_int('task.limit_count', 200)))
        self.limit_count_edit.setMaximumWidth(80)
        self.limit_count_edit.setPlaceholderText("200")
        self.limit_count_edit.setStyleSheet("""
            QLineEdit {
                font-size: 12px;
                padding: 6px 8px;
                border: 1px solid #e0e0e0;
                border-radius: 4px;
                min-height: 20px;
                text-align: center;
            }
            QLineEdit:focus {
                border: 2px solid #2196F3;
            }
        """)
        # 添加输入验证
        from PySide6.QtGui import QIntValidator
        limit_validator = QIntValidator(1, 9999)
        self.limit_count_edit.setValidator(limit_validator)
        # 添加工具提示
        self.limit_count_edit.setToolTip("设置最大采集数量 (1-9999)")
        # 连接文本变化信号
        self.limit_count_edit.textChanged.connect(self.validate_limit_count)
        params_layout.addWidget(self.limit_count_edit)

        # 停止存在计数
        params_layout.addWidget(QLabel("重复数量:"))
        self.stop_exist_count_edit = QLineEdit()
        self.stop_exist_count_edit.setText(str(settings.get_int('task.stop_exist_count', 30)))
        self.stop_exist_count_edit.setMaximumWidth(60)
        self.stop_exist_count_edit.setPlaceholderText("30")
        self.stop_exist_count_edit.setStyleSheet("""
            QLineEdit {
                font-size: 12px;
                padding: 6px 8px;
                border: 1px solid #e0e0e0;
                border-radius: 4px;
                min-height: 20px;
                text-align: center;
            }
            QLineEdit:focus {
                border: 2px solid #2196F3;
            }
        """)
        # 添加输入验证
        stop_validator = QIntValidator(1, 999)
        self.stop_exist_count_edit.setValidator(stop_validator)
        # 添加工具提示
        self.stop_exist_count_edit.setToolTip("连续遇到重复内容时停止采集 (1-999)")
        # 连接文本变化信号
        self.stop_exist_count_edit.textChanged.connect(self.validate_stop_exist_count)
        params_layout.addWidget(self.stop_exist_count_edit)

        params_row_layout.addWidget(params_group)

        # 全文下载配置组
        download_group = QGroupBox("全文下载")
        download_group.setMaximumHeight(60)
        download_group.setMinimumWidth(250)
        download_layout = QHBoxLayout(download_group)
        download_layout.setContentsMargins(8, 5, 8, 5)
        download_layout.setSpacing(8)

        # 全文下载选项
        self.enable_download_check = QCheckBox("启用")
        self.enable_download_check.setStyleSheet("""
            QCheckBox {
                font-size: 12px;
                font-weight: 500;
                color: #333;
            }
        """)
        download_layout.addWidget(self.enable_download_check)

        # 下载格式选择
        download_layout.addWidget(QLabel("格式:"))
        self.download_format_combo = QComboBox()
        self.download_format_combo.addItems(["MD + HTML", "仅MD", "仅HTML"])
        self.download_format_combo.setCurrentText("MD + HTML")
        self.download_format_combo.setMaximumWidth(150)
        self.download_format_combo.setStyleSheet("""
            QComboBox {
                font-size: 12px;
                padding: 3px;
                border: 1px solid #e0e0e0;
                border-radius: 4px;
                min-height: 20px;
                background-color: white;
            }
        """)
        download_layout.addWidget(self.download_format_combo)

        # 连接信号，当启用下载时才能选择格式
        self.enable_download_check.toggled.connect(self.on_download_enabled_changed)
        self.download_format_combo.setEnabled(False)  # 初始状态禁用

        params_row_layout.addWidget(download_group)

        # 添加弹性空间
        params_row_layout.addStretch()

        # 添加第一行到主布局
        toolbar_layout.addLayout(params_row_layout)

        # 第二行：定时任务配置行
        schedule_row_layout = QHBoxLayout()
        schedule_row_layout.setSpacing(8)

        # 定时任务配置组
        schedule_group = QGroupBox("定时任务")
        schedule_group.setMaximumHeight(60)
        schedule_group.setMinimumWidth(510)
        schedule_layout = QHBoxLayout(schedule_group)
        schedule_layout.setContentsMargins(8, 5, 8, 5)
        schedule_layout.setSpacing(8)

        # 启用定时任务
        self.enable_schedule_check = QCheckBox("启用")
        self.enable_schedule_check.setStyleSheet("""
            QCheckBox {
                font-size: 12px;
                font-weight: 500;
                color: #333;
            }
        """)
        self.enable_schedule_check.toggled.connect(self.on_schedule_toggled)
        schedule_layout.addWidget(self.enable_schedule_check)

        # Cron表达式输入
        schedule_layout.addWidget(QLabel("Cron:"))
        self.cron_input = QLineEdit()
        self.cron_input.setPlaceholderText("0 9 * * * (每天9点)")
        self.cron_input.setText(settings.get('task.cron_expression', '0 9 * * *'))
        self.cron_input.setMaximumWidth(140)
        self.cron_input.setEnabled(False)
        self.cron_input.setStyleSheet("""
            QLineEdit {
                font-size: 12px;
                padding: 6px 8px;
                border: 1px solid #e0e0e0;
                border-radius: 4px;
                min-height: 20px;
            }
            QLineEdit:disabled {
                background-color: #f5f5f5;
                color: #999999;
            }
        """)
        schedule_layout.addWidget(self.cron_input)

        # Cron帮助按钮
        self.cron_help_btn = QPushButton("?")
        self.cron_help_btn.setMaximumWidth(25)
        self.cron_help_btn.setMaximumHeight(25)
        self.cron_help_btn.setStyleSheet("""
            QPushButton {
                font-size: 12px;
                font-weight: bold;
                background-color: #2196F3;
                color: white;
                border: none;
                border-radius: 12px;
                padding: 2px 6px;
            }
            QPushButton:hover {
                background-color: #1976D2;
            }
        """)
        self.cron_help_btn.clicked.connect(self.show_cron_help)
        schedule_layout.addWidget(self.cron_help_btn)

        # 定时任务状态显示
        self.schedule_status_label = QLabel("未设置")
        self.schedule_status_label.setStyleSheet("""
            QLabel {
                font-size: 11px;
                color: #666;
                padding: 2px 6px;
                background-color: #f5f5f5;
                border-radius: 3px;
                min-width: 80px;
            }
        """)
        schedule_layout.addWidget(self.schedule_status_label)

        # 取消定时按钮
        self.cancel_schedule_btn = QPushButton("取消定时")
        self.cancel_schedule_btn.setMaximumWidth(80)
        self.cancel_schedule_btn.setStyleSheet("""
            QPushButton {
                font-size: 12px;
                background-color: #f44336;
                color: white;
                border: none;
                padding: 3px;
                border-radius: 4px;
                min-height: 25px;
                font-weight: 500;
            }
            QPushButton:hover {
                background-color: #d32f2f;
            }
            QPushButton:disabled {
                background-color: #cccccc;
                color: #666666;
            }
        """)
        self.cancel_schedule_btn.setEnabled(False)
        self.cancel_schedule_btn.clicked.connect(self.cancel_scheduled_task)
        schedule_layout.addWidget(self.cancel_schedule_btn)

        schedule_row_layout.addWidget(schedule_group)

        # 添加弹性空间，使定时任务组件左对齐
        schedule_row_layout.addStretch()

        # 添加第二行到主布局
        toolbar_layout.addLayout(schedule_row_layout)

        # 第三行：操作控制行
        control_row_layout = QHBoxLayout()
        control_row_layout.setSpacing(8)

        # 采集操作组件
        operation_group = QGroupBox("操作控制")
        operation_group.setMaximumHeight(60)
        operation_group.setMinimumWidth(510)
        operation_layout = QHBoxLayout(operation_group)
        operation_layout.setContentsMargins(8, 5, 8, 5)
        operation_layout.setSpacing(10)

        # 控制按钮
        self.start_btn = QPushButton("开始采集")
        self.start_btn.clicked.connect(self.start_collect)
        self.start_btn.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                padding: 6px 12px;
                font-weight: 400;
                font-size: 11px;
                min-width: 70px;
                min-height: 20px;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
        """)
        operation_layout.addWidget(self.start_btn)

        self.stop_btn = QPushButton("停止采集")
        self.stop_btn.clicked.connect(self.stop_collect)
        self.stop_btn.setEnabled(False)
        self.stop_btn.setStyleSheet("""
            QPushButton {
                background-color: #F44336;
                color: white;
                border: none;
                padding: 6px 12px;
                font-weight: 400;
                font-size: 11px;
                min-height: 20px;
                min-width: 70px;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #D32F2F;
                color: white;
                border: none;
                padding: 6px 12px;
                font-weight: 400;
                font-size: 11px;
                min-width: 70px;
                border-radius: 4px;
            }
            QPushButton:disabled {
                background-color: #BDBDBD;
                color: white;
                border: none;
                padding: 6px 12px;
                font-weight: 400;
                font-size: 11px;
                min-width: 70px;
                border-radius: 4px;
            }
        """)
        operation_layout.addWidget(self.stop_btn)

        # 进度显示
        progress_label = QLabel("进度:")
        progress_label.setStyleSheet("font-size: 11px; color: #333333;")
        operation_layout.addWidget(progress_label)

        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        self.progress_bar.setMaximumHeight(20)
        self.progress_bar.setStyleSheet("""
            QProgressBar {
                border: 1px solid #e0e0e0;
                text-align: center;
                font-size: 10px;
                font-weight: 400;
            }
            QProgressBar::chunk {
                background-color: #4CAF50;
            }
        """)
        operation_layout.addWidget(self.progress_bar)

        self.progress_label = QLabel("")
        self.progress_label.setStyleSheet("font-size: 11px; color: #666666;")
        operation_layout.addWidget(self.progress_label)

        control_row_layout.addWidget(operation_group)

        # 添加弹性空间，使操作组件左对齐
        control_row_layout.addStretch()

        # 添加第三行到主布局
        toolbar_layout.addLayout(control_row_layout)

        # 将工具栏添加到左侧布局的顶部
        left_layout.insertLayout(0, toolbar_layout)

        # 右侧：日志区域
        right_widget = QWidget()
        right_widget.setMinimumWidth(400)  # 设置日志区域最小宽度
        right_layout = QVBoxLayout(right_widget)
        right_layout.setContentsMargins(8, 0, 0, 0)
        right_layout.setSpacing(0)

        log_group = QGroupBox("操作日志")
        log_group.setStyleSheet("""
            QGroupBox {
                font-size: 13px;
                font-weight: 600;
                color: #333;
                border: 2px solid #e0e0e0;
                border-radius: 6px;
                padding-top: 12px;
                margin-top: 6px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 8px 0 8px;
                background-color: white;
            }
        """)
        log_layout = QVBoxLayout(log_group)
        log_layout.setContentsMargins(8, 8, 8, 8)

        self.log_widget = LogWidget()
        # 设置日志组件样式
        self.log_widget.setStyleSheet("""
            QTextEdit {
                font-size: 12px;
                font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
                line-height: 1.4;
                border: 1px solid #d0d0d0;
                border-radius: 4px;
                background-color: #fafafa;
                padding: 10px;
                min-height: 400px;
                min-width: 350px;
            }
            QScrollBar:vertical {
                background-color: #f0f0f0;
                width: 12px;
                border-radius: 6px;
            }
            QScrollBar::handle:vertical {
                background-color: #c0c0c0;
                border-radius: 6px;
                min-height: 20px;
            }
            QScrollBar::handle:vertical:hover {
                background-color: #a0a0a0;
            }
            QScrollBar:horizontal {
                background-color: #f0f0f0;
                height: 12px;
                border-radius: 6px;
            }
            QScrollBar::handle:horizontal {
                background-color: #c0c0c0;
                border-radius: 6px;
                min-width: 20px;
            }
            QScrollBar::handle:horizontal:hover {
                background-color: #a0a0a0;
            }
        """)
        log_layout.addWidget(self.log_widget)

        right_layout.addWidget(log_group)
        main_splitter.addWidget(right_widget)

        # 设置分割器比例 (左:右 = 1:1，日志区域占一半空间)
        main_splitter.setSizes([450, 450])
        main_splitter.setStretchFactor(0, 1)  # 左侧可拉伸因子为1
        main_splitter.setStretchFactor(1, 1)  # 右侧可拉伸因子为1

        layout.addWidget(main_splitter)

        # 连接日志信号
        self.log_message.connect(self.log_widget.add_log)

    def get_limit_count(self):
        """获取最大采集数量"""
        try:
            value = int(self.limit_count_edit.text().strip())
            return max(1, min(value, 9999))  # 限制在1-9999范围内
        except (ValueError, AttributeError):
            return 200  # 默认值

    def get_stop_exist_count(self):
        """获取重复停止数量"""
        try:
            value = int(self.stop_exist_count_edit.text().strip())
            return max(1, min(value, 999))  # 限制在1-999范围内
        except (ValueError, AttributeError):
            return 30  # 默认值

    def validate_limit_count(self, text):
        """验证最大数量输入"""
        if not text.strip():
            return
        try:
            value = int(text)
            if value < 1 or value > 9999:
                self.limit_count_edit.setStyleSheet("""
                    QLineEdit {
                        font-size: 12px;
                        padding: 6px 8px;
                        border: 2px solid #f44336;
                        border-radius: 4px;
                        min-height: 20px;
                        text-align: center;
                        background-color: #ffebee;
                    }
                """)
            else:
                self.limit_count_edit.setStyleSheet("""
                    QLineEdit {
                        font-size: 12px;
                        padding: 6px 8px;
                        border: 1px solid #e0e0e0;
                        border-radius: 4px;
                        min-height: 20px;
                        text-align: center;
                    }
                    QLineEdit:focus {
                        border: 2px solid #2196F3;
                    }
                """)
        except ValueError:
            self.limit_count_edit.setStyleSheet("""
                QLineEdit {
                    font-size: 12px;
                    padding: 6px 8px;
                    border: 2px solid #f44336;
                    border-radius: 4px;
                    min-height: 20px;
                    text-align: center;
                    background-color: #ffebee;
                }
            """)

    def validate_stop_exist_count(self, text):
        """验证重复数量输入"""
        if not text.strip():
            return
        try:
            value = int(text)
            if value < 1 or value > 999:
                self.stop_exist_count_edit.setStyleSheet("""
                    QLineEdit {
                        font-size: 12px;
                        padding: 6px 8px;
                        border: 2px solid #f44336;
                        border-radius: 4px;
                        min-height: 20px;
                        text-align: center;
                        background-color: #ffebee;
                    }
                """)
            else:
                self.stop_exist_count_edit.setStyleSheet("""
                    QLineEdit {
                        font-size: 12px;
                        padding: 6px 8px;
                        border: 1px solid #e0e0e0;
                        border-radius: 4px;
                        min-height: 20px;
                        text-align: center;
                    }
                    QLineEdit:focus {
                        border: 2px solid #2196F3;
                    }
                """)
        except ValueError:
            self.stop_exist_count_edit.setStyleSheet("""
                QLineEdit {
                    font-size: 12px;
                    padding: 6px 8px;
                    border: 2px solid #f44336;
                    border-radius: 4px;
                    min-height: 20px;
                    text-align: center;
                    background-color: #ffebee;
                }
            """)

        # 初始化调度器
        self.scheduler = get_scheduler()
        self.scheduler.task_triggered.connect(self.on_scheduled_task_triggered)
        self.scheduler.start()

        # 启动状态更新定时器
        self.status_timer = QTimer()
        self.status_timer.timeout.connect(self.update_schedule_display)
        self.status_timer.start(60000)  # 每分钟更新一次状态显示

    def load_official_infos(self):
        """加载公众号信息列表"""
        self.official_list.clear()
        official_infos = self.official_service.load_official_infos()

        for i, info in enumerate(official_infos):
            item = QListWidgetItem()
            checkbox = QCheckBox(f"{info.official_name} ({info.official_account})")
            checkbox.setProperty("official_account", info.official_account)

            # 设置工具提示
            item.setToolTip(f"公众号: {info.official_name}\n账号ID: {info.official_account}\n勾选以包含在采集任务中")

            self.official_list.addItem(item)
            self.official_list.setItemWidget(item, checkbox)

        self.log_message.emit(f"加载了 {len(official_infos)} 个公众号", "INFO")

    def select_all_officials(self):
        """全选公众号"""
        for i in range(self.official_list.count()):
            item = self.official_list.item(i)
            checkbox = self.official_list.itemWidget(item)
            if checkbox:
                checkbox.setChecked(True)

    def deselect_all_officials(self):
        """取消全选公众号"""
        for i in range(self.official_list.count()):
            item = self.official_list.item(i)
            checkbox = self.official_list.itemWidget(item)
            if checkbox:
                checkbox.setChecked(False)

    def get_selected_officials(self):
        """获取选中的公众号"""
        selected_accounts = []
        for i in range(self.official_list.count()):
            item = self.official_list.item(i)
            checkbox = self.official_list.itemWidget(item)
            if checkbox and checkbox.isChecked():
                account = checkbox.property("official_account")
                selected_accounts.append(account)

        # 获取完整的公众号信息
        all_officials = self.official_service.load_official_infos()
        selected_officials = [
            info for info in all_officials
            if info.official_account in selected_accounts
        ]

        return selected_officials

    def start_collect(self):
        """开始采集"""
        # 检查是否已有定时任务在运行
        if hasattr(self, 'scheduled_officials') and self.start_btn.text() == "立即执行":
            # 立即执行已设置的定时任务
            self.execute_scheduled_collect()
            return

        # 检查是否启用定时任务
        if self.enable_schedule_check.isChecked():
            self.setup_scheduled_collect()
            return

        # 立即执行采集
        self.execute_collect()

    def setup_scheduled_collect(self):
        """设置定时采集任务"""
        cron_expression = self.cron_input.text().strip()
        if not cron_expression:
            CustomMessageBox.warning(self, "警告", "请输入Cron表达式")
            return

        # 验证Cron表达式
        is_valid, message = validate_cron_expression(cron_expression)
        if not is_valid:
            CustomMessageBox.warning(self, "Cron表达式错误", message)
            return

        selected_officials = self.get_selected_officials()
        if not selected_officials:
            CustomMessageBox.warning(self, "警告", "请选择要采集的公众号")
            return

        # 保存定时任务配置
        self.scheduled_officials = selected_officials
        self.scheduled_limit_count = self.get_limit_count()
        self.scheduled_stop_exist_count = self.get_stop_exist_count()

        # 添加到调度器
        task_id = "collect_task"
        self.scheduler.remove_task(task_id)  # 先移除已存在的任务
        success = self.scheduler.add_task(
            task_id,
            cron_expression,
            self.execute_scheduled_collect
        )

        if success:
            # 保存配置到设置
            settings.set('task.cron_expression', cron_expression)

            # 获取任务信息并更新UI状态
            task_info = self.scheduler.get_task_info(task_id)
            next_run = task_info['next_run'].strftime('%Y-%m-%d %H:%M')

            # 更新状态显示
            self.update_schedule_status(f"下次: {next_run}")
            self.cancel_schedule_btn.setEnabled(True)

            # 更新开始按钮状态
            self.start_btn.setText("立即执行")
            self.start_btn.setStyleSheet("""
                QPushButton {
                    background-color: #FF9800;
                    color: white;
                    border: none;
                    padding: 8px 16px;
                    border-radius: 4px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #F57C00;
                }
            """)

            self.log_message.emit(f"定时采集任务已设置，下次运行时间: {next_run}", "INFO")
            CustomMessageBox.information(self, "定时任务", f"定时采集任务已设置\n{message}\n\n下次运行时间: {next_run}")
        else:
            CustomMessageBox.critical(self, "错误", "设置定时任务失败")

    def execute_collect(self):
        """执行采集任务"""
        selected_officials = self.get_selected_officials()

        if not selected_officials:
            CustomMessageBox.warning(self, "警告", "请选择要采集的公众号")
            return

        if self.task_manager.is_task_running():
            CustomMessageBox.warning(self, "警告", "已有任务正在运行")
            return

        # 获取采集参数
        limit_count = self.get_limit_count()
        stop_exist_count = self.get_stop_exist_count()

        # 确认对话框
        reply = CustomMessageBox.question(
            self, "确认采集",
            f"确定要采集 {len(selected_officials)} 个公众号吗？"
        )

        if reply != CustomMessageBox.Yes:
            return

        # 配置全文下载设置
        download_settings = self.get_download_settings()
        if download_settings and download_settings['enabled']:
            try:
                official_process.set_download_config(
                    enable=True,
                    format_type=self.download_format_combo.currentText(),
                    delay=2
                )
                self.log_widget.add_log("已启用全文下载功能", "INFO")
            except ImportError:
                self.log_widget.add_log("全文下载模块导入失败", "ERROR")
        else:
            try:
                official_process.set_download_config(enable=False)
                self.log_widget.add_log("已禁用全文下载功能", "INFO")
            except ImportError:
                pass

        try:
            # 启动采集任务
            self.current_task = self.task_manager.start_collect_task(
                selected_officials, limit_count, stop_exist_count
            )

            # 连接信号
            self.current_task.signals.progress.connect(self.update_progress)
            self.current_task.signals.finished.connect(self.collect_finished)
            self.current_task.signals.log.connect(lambda msg: self.log_message.emit(msg, "INFO"))

            # 添加调试信息
            print("[DEBUG] 采集管理器已连接任务信号")

            # 更新UI状态
            self.start_btn.setEnabled(False)
            self.stop_btn.setEnabled(True)
            self.progress_bar.setVisible(True)
            self.progress_bar.setValue(0)

            # 采集期间禁用取消定时按钮
            self.cancel_schedule_btn.setEnabled(False)

            self.log_message.emit(f"开始采集任务，共 {len(selected_officials)} 个公众号", "INFO")
            
        except Exception as e:
            CustomMessageBox.critical(self, "错误", f"启动采集任务失败: {str(e)}")
            self.log_message.emit(f"启动采集任务失败: {str(e)}", "ERROR")
    
    def stop_collect(self):
        """停止采集"""
        if self.current_task:
            reply = CustomMessageBox.question(
                self, "确认停止", "确定要停止采集任务吗？"
            )

            if reply == CustomMessageBox.Yes:
                print("[DEBUG] 采集管理器开始取消任务")
                self.task_manager.cancel_current_task()
                self.log_message.emit("用户取消了采集任务", "INFO")
                print("[DEBUG] 采集管理器已发送取消请求")

                # 清理任务引用
                self.current_task = None

                # 检查是否有定时任务在运行，恢复正确的UI状态
                has_scheduled_task = hasattr(self, 'scheduled_officials') and self.scheduler.get_task_info("collect_task")

                if has_scheduled_task:
                    # 有定时任务时，恢复为"立即执行"状态
                    self.start_btn.setText("立即执行")
                    self.start_btn.setStyleSheet("""
                        QPushButton {
                            background-color: #FF9800;
                            color: white;
                            border: none;
                            padding: 8px 16px;
                            border-radius: 4px;
                            font-weight: bold;
                        }
                        QPushButton:hover {
                            background-color: #F57C00;
                        }
                    """)
                    self.start_btn.setEnabled(True)
                    # 启用取消定时按钮
                    self.cancel_schedule_btn.setEnabled(True)
                else:
                    # 没有定时任务时，恢复为"开始采集"状态
                    self.start_btn.setText("开始采集")
                    self.start_btn.setStyleSheet("""
                        QPushButton {
                            background-color: #4CAF50;
                            color: white;
                            border: none;
                            padding: 8px 16px;
                            border-radius: 4px;
                            font-weight: bold;
                        }
                        QPushButton:hover {
                            background-color: #45a049;
                        }
                    """)
                    self.start_btn.setEnabled(True)
                    # 禁用取消定时按钮
                    self.cancel_schedule_btn.setEnabled(False)

                # 恢复其他UI状态
                self.stop_btn.setEnabled(False)
                self.progress_bar.setVisible(False)
                self.progress_label.setText("")

                print("[DEBUG] 采集管理器UI状态已恢复")
    
    def update_progress(self, value: int, message: str):
        """更新进度"""
        self.progress_bar.setValue(value)
        self.progress_label.setText(message)

    def on_download_enabled_changed(self, enabled):
        """全文下载启用状态变化处理"""
        self.download_format_combo.setEnabled(enabled)
        if enabled:
            self.log_widget.add_log("已启用全文下载功能", "INFO")
        else:
            self.log_widget.add_log("已禁用全文下载功能", "INFO")

    def on_schedule_toggled(self, enabled):
        """定时任务启用状态改变"""
        self.cron_input.setEnabled(enabled)
        if enabled:
            self.cron_input.setStyleSheet("""
                QLineEdit {
                    font-size: 12px;
                    padding: 6px 8px;
                    border: 1px solid #e0e0e0;
                    border-radius: 4px;
                    min-height: 20px;
                    background-color: white;
                }
            """)
            self.log_widget.add_log("已启用定时任务功能", "INFO")
        else:
            self.cron_input.setStyleSheet("""
                QLineEdit {
                    font-size: 12px;
                    padding: 6px 8px;
                    border: 1px solid #e0e0e0;
                    border-radius: 4px;
                    min-height: 20px;
                    background-color: #f5f5f5;
                    color: #999999;
                }
            """)
            # 如果禁用定时任务，同时取消已设置的定时任务
            if hasattr(self, 'scheduler') and self.scheduler:
                self.scheduler.remove_task("collect_task")
                self.update_schedule_status("未设置")
                self.cancel_schedule_btn.setEnabled(False)
            self.log_widget.add_log("已禁用定时任务功能", "INFO")

    def show_cron_help(self):
        """显示Cron表达式帮助"""
        from ui.message_box import CustomMessageBox

        help_text = """Cron表达式格式：分 时 日 月 周

示例：
• 0 9 * * *     - 每天9点
• 0 9,18 * * *  - 每天9点和18点
• 0 9 * * 1-5   - 工作日9点
• 0 */2 * * *   - 每2小时
• 30 8 1 * *    - 每月1号8:30
• 0 9 * * 0     - 每周日9点

字段说明：
• 分钟：0-59
• 小时：0-23
• 日期：1-31
• 月份：1-12
• 星期：0-7 (0和7都表示周日)

特殊字符：
• * 表示任意值
• , 表示多个值
• - 表示范围
• / 表示间隔"""

        CustomMessageBox.information(self, "Cron表达式帮助", help_text.strip())

    def cancel_scheduled_task(self):
        """取消定时任务"""
        try:
            # 检查是否有任务正在运行
            if self.current_task:
                # 如果有任务正在运行，询问是否同时停止当前任务
                reply = CustomMessageBox.question(
                    self, "确认取消",
                    "当前有采集任务正在运行。\n确定要取消定时任务并停止当前采集吗？"
                )

                if reply == CustomMessageBox.Yes:
                    # 停止当前任务
                    self.task_manager.cancel_current_task()
                    self.current_task = None
                    self.log_message.emit("已停止当前采集任务", "INFO")
                else:
                    return
            else:
                # 没有任务运行时的普通确认
                reply = CustomMessageBox.question(
                    self, "确认取消",
                    "确定要取消定时采集任务吗？"
                )

                if reply != CustomMessageBox.Yes:
                    return

            # 从调度器中移除任务
            if hasattr(self, 'scheduler') and self.scheduler:
                self.scheduler.remove_task("collect_task")

            # 清理定时任务相关数据
            if hasattr(self, 'scheduled_officials'):
                delattr(self, 'scheduled_officials')
            if hasattr(self, 'scheduled_limit_count'):
                delattr(self, 'scheduled_limit_count')
            if hasattr(self, 'scheduled_stop_exist_count'):
                delattr(self, 'scheduled_stop_exist_count')

            # 更新UI状态
            self.update_schedule_status("未设置")
            self.cancel_schedule_btn.setEnabled(False)
            self.enable_schedule_check.setChecked(False)

            # 恢复开始按钮状态为普通采集模式
            self.start_btn.setText("开始采集")
            self.start_btn.setStyleSheet("""
                QPushButton {
                    background-color: #4CAF50;
                    color: white;
                    border: none;
                    padding: 8px 16px;
                    border-radius: 4px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #45a049;
                }
                QPushButton:disabled {
                    background-color: #cccccc;
                    color: #666666;
                }
            """)
            self.start_btn.setEnabled(True)

            # 恢复其他UI状态
            self.stop_btn.setEnabled(False)
            self.progress_bar.setVisible(False)
            self.progress_label.setText("")

            self.log_widget.add_log("定时采集任务已取消", "INFO")
            CustomMessageBox.information(self, "取消成功", "定时采集任务已成功取消")

        except Exception as e:
            self.log_widget.add_log(f"取消定时任务失败: {str(e)}", "ERROR")
            CustomMessageBox.critical(self, "错误", f"取消定时任务失败：{str(e)}")

    def update_schedule_status(self, status_text: str):
        """更新定时任务状态显示"""
        self.schedule_status_label.setText(status_text)

        if status_text == "未设置":
            self.schedule_status_label.setStyleSheet("""
                QLabel {
                    font-size: 11px;
                    color: #666;
                    padding: 2px 6px;
                    background-color: #f5f5f5;
                    border-radius: 3px;
                    min-width: 80px;
                }
            """)
        else:
            self.schedule_status_label.setStyleSheet("""
                QLabel {
                    font-size: 11px;
                    color: #2196F3;
                    padding: 2px 6px;
                    background-color: #e3f2fd;
                    border-radius: 3px;
                    min-width: 80px;
                    font-weight: 500;
                }
            """)

    def execute_scheduled_collect(self):
        """执行定时采集任务"""
        if hasattr(self, 'scheduled_officials'):
            self.log_message.emit("定时任务触发，开始执行采集", "INFO")

            # 设置UI日志回调
            try:
                from wechat.auto_process.officical_process import set_ui_log_callback
                set_ui_log_callback(self.log_widget.add_log)
            except:
                pass

            # 配置全文下载设置
            download_settings = self.get_download_settings()
            if download_settings and download_settings['enabled']:
                try:
                    from wechat.auto_process.officical_process import set_download_config
                    set_download_config(
                        enable=True,
                        format_type=self.download_format_combo.currentText(),
                        delay=2
                    )
                except ImportError:
                    pass

            try:
                # 启动采集任务
                self.current_task = self.task_manager.start_collect_task(
                    self.scheduled_officials,
                    self.scheduled_limit_count,
                    self.scheduled_stop_exist_count
                )

                # 连接信号
                self.current_task.signals.progress.connect(self.update_progress)
                self.current_task.signals.finished.connect(self.collect_finished)
                self.current_task.signals.log.connect(lambda msg: self.log_message.emit(msg, "INFO"))

                # 更新UI状态
                self.start_btn.setEnabled(False)
                self.stop_btn.setEnabled(True)
                self.progress_bar.setVisible(True)
                self.progress_bar.setValue(0)

                # 采集期间禁用取消定时按钮
                self.cancel_schedule_btn.setEnabled(False)

                self.log_message.emit(f"定时任务开始采集 {len(self.scheduled_officials)} 个公众号", "INFO")

            except Exception as e:
                self.log_message.emit(f"定时任务执行失败：{str(e)}", "ERROR")

    def on_scheduled_task_triggered(self, task_id: str):
        """定时任务触发处理"""
        if task_id == "collect_task":
            self.log_message.emit("定时采集任务已触发", "INFO")

    def update_schedule_display(self):
        """更新定时任务显示状态"""
        if hasattr(self, 'scheduler') and self.scheduler:
            task_info = self.scheduler.get_task_info("collect_task")
            if task_info:
                next_run = task_info['next_run'].strftime('%Y-%m-%d %H:%M')
                self.update_schedule_status(f"下次: {next_run}")
            else:
                if self.schedule_status_label.text() != "未设置":
                    self.update_schedule_status("未设置")
                    self.cancel_schedule_btn.setEnabled(False)

    def get_download_settings(self):
        """获取下载设置"""
        if not self.enable_download_check.isChecked():
            return None

        format_text = self.download_format_combo.currentText()
        settings = {
            'enabled': True,
            'save_md': 'MD' in format_text,
            'save_html': 'HTML' in format_text
        }
        return settings

    def convert_to_markdown(self, content, article):
        """转换为Markdown格式"""
        md_content = f"# {article.get('title', '未知标题')}\n\n"
        md_content += f"**作者**: {article.get('author', '未知')}\n"
        md_content += f"**发布时间**: {article.get('time', '未知')}\n"
        md_content += f"**原文链接**: {article.get('link', '未知')}\n\n"
        md_content += "---\n\n"
        md_content += content.get('text', '')
        return md_content

    def convert_to_html(self, content, article):
        """转换为HTML格式"""
        html_content = f"""<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{article.get('title', '未知标题')}</title>
    <style>
        body {{ font-family: 'Microsoft YaHei', sans-serif; line-height: 1.6; margin: 40px; }}
        h1 {{ color: #333; border-bottom: 2px solid #4CAF50; padding-bottom: 10px; }}
        .meta {{ color: #666; font-size: 14px; margin-bottom: 20px; }}
        .content {{ margin-top: 20px; }}
    </style>
</head>
<body>
    <h1>{article.get('title', '未知标题')}</h1>
    <div class="meta">
        <p><strong>作者</strong>: {article.get('author', '未知')}</p>
        <p><strong>发布时间</strong>: {article.get('time', '未知')}</p>
        <p><strong>原文链接</strong>: <a href="{article.get('link', '#')}">{article.get('link', '未知')}</a></p>
    </div>
    <div class="content">
        {content.get('html', content.get('text', '').replace(chr(10), '<br>'))}
    </div>
</body>
</html>"""
        return html_content

    def collect_finished(self, success: bool, message: str):
        """采集任务完成"""
        # 清理任务引用
        self.current_task = None

        # 检查是否有定时任务在运行
        has_scheduled_task = hasattr(self, 'scheduled_officials') and self.scheduler.get_task_info("collect_task")

        if has_scheduled_task:
            # 有定时任务时，恢复为"立即执行"状态
            self.start_btn.setText("立即执行")
            self.start_btn.setStyleSheet("""
                QPushButton {
                    background-color: #FF9800;
                    color: white;
                    border: none;
                    padding: 8px 16px;
                    border-radius: 4px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #F57C00;
                }
            """)
            self.start_btn.setEnabled(True)
            # 启用取消定时按钮
            self.cancel_schedule_btn.setEnabled(True)
        else:
            # 没有定时任务时，恢复为"开始采集"状态
            self.start_btn.setText("开始采集")
            self.start_btn.setStyleSheet("""
                QPushButton {
                    background-color: #4CAF50;
                    color: white;
                    border: none;
                    padding: 8px 16px;
                    border-radius: 4px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #45a049;
                }
            """)
            self.start_btn.setEnabled(True)
            # 禁用取消定时按钮
            self.cancel_schedule_btn.setEnabled(False)

        # 恢复停止按钮状态
        self.stop_btn.setEnabled(False)

        # 隐藏进度显示
        self.progress_bar.setVisible(False)
        self.progress_label.setText("")

        # 显示结果
        if success:
            self.log_message.emit(message, "INFO")
            CustomMessageBox.information(self, "任务完成", message)
        else:
            self.log_message.emit(message, "ERROR")
            CustomMessageBox.warning(self, "任务失败", message)
