"""
自定义消息框工具类
提供统一的消息框样式和大小
"""

from PySide6.QtWidgets import QMessageBox, QApplication, QGraphicsDropShadowEffect
from PySide6.QtCore import Qt, QPropertyAnimation, QEasingCurve, QRect
from PySide6.QtGui import QFont, QColor


class CustomMessageBox:
    """自定义消息框类"""

    # 定义返回值常量
    Ok = QMessageBox.StandardButton.Ok
    Cancel = QMessageBox.StandardButton.Cancel
    Yes = QMessageBox.StandardButton.Yes
    No = QMessageBox.StandardButton.No
    
    @staticmethod
    def _setup_message_box(msg_box: QMessageBox, title: str, text: str, width: int = 260, height: int = 80):
        """设置消息框的基本属性"""
        msg_box.setWindowTitle(title)
        msg_box.setText(text)

        # 设置窗口大小
        msg_box.setMinimumSize(width, height)
        msg_box.resize(width, height)

        # 设置字体
        font = QFont("Microsoft YaHei", 10)
        msg_box.setFont(font)

        # 设置为无图标模式
        msg_box.setIcon(QMessageBox.Icon.NoIcon)

        # 设置窗口标志，去除问号按钮
        msg_box.setWindowFlags(msg_box.windowFlags() & ~Qt.WindowType.WindowContextHelpButtonHint)

        # 添加阴影效果
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(6)
        shadow.setColor(QColor(0, 0, 0, 25))
        shadow.setOffset(0, 1)
        msg_box.setGraphicsEffect(shadow)
        
        # 设置紧凑样式
        msg_box.setStyleSheet("""
            QMessageBox {
                background-color: #ffffff;
                border: none;
                border-radius: 6px;
                font-size: 9px;
                padding: 0px;
            }
            QMessageBox QLabel {
                font-size: 14px;
                color: #333333;
                padding: 1px;
                line-height: 1.3;
                min-width: 280px;
                background-color: transparent;
                font-weight: 400;
            }
            QMessageBox QLabel#qt_msgbox_label {
                background-color: #ffffff;
                border-top-left-radius: 6px;
                border-top-right-radius: 6px;
            }
            QMessageBox QPushButton {
                font-size: 14px;
                font-weight: 600;
                min-width: 80px;
                min-height: 32px;
                padding: 8px 16px;
                border-radius: 6px;
                border: none;
                margin: 3px 6px;
                font-family: 'Microsoft YaHei', sans-serif;
                color: white;
            }
            /* 确定/OK按钮 - 主要操作 */
            QMessageBox QPushButton[text="确定"],
            QMessageBox QPushButton[text="OK"] {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #007bff, stop:1 #0056b3);
                color: white;
                font-weight: bold;
            }
            QMessageBox QPushButton[text="确定"]:hover,
            QMessageBox QPushButton[text="OK"]:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #1a8cff, stop:1 #007bff);
            }
            QMessageBox QPushButton[text="确定"]:pressed,
            QMessageBox QPushButton[text="OK"]:pressed {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #0056b3, stop:1 #004085);
            }

            /* 取消/Cancel按钮 - 次要操作 */
            QMessageBox QPushButton[text="取消"],
            QMessageBox QPushButton[text="Cancel"] {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #f8f9fa, stop:1 #e9ecef);
                color: #495057;
                border: 1px solid #dee2e6;
            }
            QMessageBox QPushButton[text="取消"]:hover,
            QMessageBox QPushButton[text="Cancel"]:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #ffffff, stop:1 #f8f9fa);
                border: 1px solid #adb5bd;
            }

            /* 是/Yes按钮 - 确认操作 */
            QMessageBox QPushButton[text="是"],
            QMessageBox QPushButton[text="Yes"] {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #28a745, stop:1 #1e7e34);
                color: white;
                font-weight: bold;
            }
            QMessageBox QPushButton[text="是"]:hover,
            QMessageBox QPushButton[text="Yes"]:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #34ce57, stop:1 #28a745);
            }

            /* 否/No按钮 - 拒绝操作 */
            QMessageBox QPushButton[text="否"],
            QMessageBox QPushButton[text="No"] {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #6c757d, stop:1 #495057);
                color: white;
                font-weight: bold;
            }
            QMessageBox QPushButton[text="否"]:hover,
            QMessageBox QPushButton[text="No"]:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #7c858d, stop:1 #6c757d);
            }

            /* 按钮区域样式 */
            QMessageBox QDialogButtonBox {
                background-color: #f8f9fa;
                border-bottom-left-radius: 6px;
                border-bottom-right-radius: 6px;
                padding: 0px;
            }
            
            QPushButton {
                background-color: #2196F3;
                color: white;
                border: none;
                padding: 8px 12px;
                font-weight: 400;
                min-width: 60px;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #1976D2;
            }
            QPushButton:pressed {
                background-color: #1565C0;
            }
        """)
        
        # 居中显示
        if QApplication.activeWindow():
            parent = QApplication.activeWindow()
            parent_geometry = parent.geometry()
            x = parent_geometry.x() + (parent_geometry.width() - width) // 2
            y = parent_geometry.y() + (parent_geometry.height() - height) // 2
            msg_box.move(x, y)
    
    @staticmethod
    def information(parent, title: str, text: str, width: int = 260, height: int = 80):
        """显示信息提示框"""
        msg_box = QMessageBox(QMessageBox.Information, title, text, CustomMessageBox.Ok, parent)
        CustomMessageBox._setup_message_box(msg_box, title, text, width, height)

        return msg_box.exec()
    
    @staticmethod
    def warning(parent, title: str, text: str, width: int = 260, height: int = 80):
        """显示警告提示框"""
        msg_box = QMessageBox(QMessageBox.Warning, title, text, CustomMessageBox.Ok, parent)
        CustomMessageBox._setup_message_box(msg_box, title, text, width, height)

        return msg_box.exec()
    
    @staticmethod
    def critical(parent, title: str, text: str, width: int = 260, height: int = 80):
        """显示错误提示框"""
        msg_box = QMessageBox(QMessageBox.Icon.Critical, title, text, CustomMessageBox.Ok, parent)
        CustomMessageBox._setup_message_box(msg_box, title, text, width, height)

        return msg_box.exec()
    
    @staticmethod
    def question(parent, title: str, text: str, width: int = 260, height: int = 80):
        """显示确认提示框"""
        msg_box = QMessageBox(QMessageBox.Icon.Question, title, text,
                             CustomMessageBox.Yes | CustomMessageBox.No, parent)
        CustomMessageBox._setup_message_box(msg_box, title, text, width, height)
        return msg_box.exec()
    
    @staticmethod
    def question_with_cancel(parent, title: str, text: str, width: int = 260, height: int = 80):
        """显示带取消的确认提示框"""
        msg_box = QMessageBox(QMessageBox.Icon.Question, title, text,
                             CustomMessageBox.Yes | QMessageBox.No | CustomMessageBox.Cancel, parent)
        CustomMessageBox._setup_message_box(msg_box, title, text, width, height)
        return msg_box.exec()
