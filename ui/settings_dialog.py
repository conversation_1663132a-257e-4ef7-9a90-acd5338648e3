"""
设置对话框
"""
from PySide6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QFormLayout,
                               QLineEdit, QSpinBox, QCheckBox, QPushButton,
                               QGroupBox, QFileDialog, QMessageBox, QTabWidget,
                               QWidget, QComboBox, QLabel)
from PySide6.QtCore import Qt
from PySide6.QtGui import QGuiApplication
from config.settings import settings
from ui.message_box import CustomMessageBox


class SettingsDialog(QDialog):
    """设置对话框"""



    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("设置")
        self.setModal(True)
        self.resize(500, 400)
        self.setup_ui()
        self.load_settings()
    
    def setup_ui(self):
        """设置UI"""
        layout = QVBoxLayout(self)
        
        # 创建标签页
        tab_widget = QTabWidget()
        
        # 微信设置标签页
        wechat_tab = self.create_wechat_tab()
        tab_widget.addTab(wechat_tab, "微信设置")

        # 采集设置标签页（原任务设置 + 数据文件设置）
        collect_tab = self.create_collect_tab()
        tab_widget.addTab(collect_tab, "采集设置")
        
        layout.addWidget(tab_widget)
        
        # 按钮区域
        button_layout = QHBoxLayout()
        
        self.ok_btn = QPushButton("确定")
        self.ok_btn.clicked.connect(self.accept)
        button_layout.addWidget(self.ok_btn)
        
        self.cancel_btn = QPushButton("取消")
        self.cancel_btn.clicked.connect(self.reject)
        button_layout.addWidget(self.cancel_btn)
        
        self.apply_btn = QPushButton("应用")
        self.apply_btn.clicked.connect(self.apply_settings)
        button_layout.addWidget(self.apply_btn)
        
        layout.addLayout(button_layout)
    
    def create_wechat_tab(self):
        """创建微信设置标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 微信路径设置
        path_group = QGroupBox("微信路径设置")
        path_layout = QFormLayout(path_group)
        
        path_input_layout = QHBoxLayout()
        self.wechat_path_edit = QLineEdit()
        path_input_layout.addWidget(self.wechat_path_edit)

        browse_btn = QPushButton("浏览")
        browse_btn.clicked.connect(self.browse_wechat_path)
        path_input_layout.addWidget(browse_btn)

        auto_detect_btn = QPushButton("自动检测")
        auto_detect_btn.clicked.connect(self.auto_detect_wechat_path)
        path_input_layout.addWidget(auto_detect_btn)

        path_layout.addRow("微信程序路径:", path_input_layout)
        
        layout.addWidget(path_group)
        
        # 微信操作设置
        operation_group = QGroupBox("操作设置")
        operation_layout = QFormLayout(operation_group)
        
        self.load_delay_spin = QSpinBox()
        self.load_delay_spin.setRange(1, 10)
        self.load_delay_spin.setSuffix(" 秒")
        operation_layout.addRow("加载延迟:", self.load_delay_spin)
        
        self.maximize_check = QCheckBox("启动时最大化微信窗口")
        operation_layout.addRow("", self.maximize_check)
        
        layout.addWidget(operation_group)
        
        layout.addStretch()
        return widget
    
    def create_collect_tab(self):
        """创建采集设置标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # 采集参数设置
        collect_group = QGroupBox("采集参数")
        collect_layout = QFormLayout(collect_group)

        self.limit_count_spin = QSpinBox()
        self.limit_count_spin.setRange(1, 1000)
        collect_layout.addRow("默认最大采集数量:", self.limit_count_spin)

        self.stop_exist_count_spin = QSpinBox()
        self.stop_exist_count_spin.setRange(1, 100)
        collect_layout.addRow("默认停止存在计数:", self.stop_exist_count_spin)

        layout.addWidget(collect_group)

        # 数据文件设置
        data_group = QGroupBox("数据文件")
        data_layout = QFormLayout(data_group)

        # 账号文件设置
        accounts_layout = QHBoxLayout()
        self.accounts_file_edit = QLineEdit()
        accounts_layout.addWidget(self.accounts_file_edit)

        accounts_browse_btn = QPushButton("浏览")
        accounts_browse_btn.clicked.connect(self.browse_accounts_file)
        accounts_layout.addWidget(accounts_browse_btn)

        data_layout.addRow("账号文件:", accounts_layout)

        # 公众号信息文件设置
        official_layout = QHBoxLayout()
        self.official_infos_file_edit = QLineEdit()
        official_layout.addWidget(self.official_infos_file_edit)

        official_browse_btn = QPushButton("浏览")
        official_browse_btn.clicked.connect(self.browse_official_file)
        official_layout.addWidget(official_browse_btn)

        data_layout.addRow("公众号信息文件:", official_layout)

        layout.addWidget(data_group)

        layout.addStretch()
        return widget
    




    def browse_wechat_path(self):
        """浏览微信路径"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择微信程序", "", "可执行文件 (*.exe);;所有文件 (*)"
        )

        if file_path:
            self.wechat_path_edit.setText(file_path)

    def browse_accounts_file(self):
        """浏览账号文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择账号文件", "", "CSV文件 (*.csv);;所有文件 (*)"
        )

        if file_path:
            self.accounts_file_edit.setText(file_path)

    def browse_official_file(self):
        """浏览公众号信息文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择公众号信息文件", "", "JSON文件 (*.json);;所有文件 (*)"
        )

        if file_path:
            self.official_infos_file_edit.setText(file_path)

    def auto_detect_wechat_path(self):
        """自动检测微信路径"""
        try:
            from core.wechat_detector import wechat_detector

            # 获取所有可能的路径
            possible_paths = wechat_detector.get_all_possible_paths()

            if not possible_paths:
                QMessageBox.warning(self, "检测失败", "未能检测到微信安装路径，请手动选择。")
                return

            if len(possible_paths) == 1:
                # 只有一个路径，直接使用
                self.wechat_path_edit.setText(possible_paths[0])
                QMessageBox.information(self, "检测成功", f"已检测到微信路径:\n{possible_paths[0]}")
            else:
                # 多个路径，让用户选择
                from PySide6.QtWidgets import QInputDialog

                path, ok = QInputDialog.getItem(
                    self, "选择微信路径",
                    "检测到多个可能的微信路径，请选择:",
                    possible_paths, 0, False
                )

                if ok and path:
                    self.wechat_path_edit.setText(path)
                    QMessageBox.information(self, "设置成功", f"已设置微信路径:\n{path}")

        except Exception as e:
            QMessageBox.critical(self, "检测失败", f"自动检测微信路径时出错:\n{str(e)}")
    
    def load_settings(self):
        """加载设置"""
        # 微信设置
        self.wechat_path_edit.setText(settings.get_str('wechat.wechat_path', ''))
        self.load_delay_spin.setValue(settings.get_int('wechat.load_delay', 3))
        self.maximize_check.setChecked(settings.get_bool('wechat.is_maximize', False))

        # 采集设置
        self.limit_count_spin.setValue(settings.get_int('task.limit_count', 200))
        self.stop_exist_count_spin.setValue(settings.get_int('task.stop_exist_count', 30))

        # 数据文件设置
        self.accounts_file_edit.setText(settings.get_str('data.accounts_file', 'resource/accounts.csv'))
        self.official_infos_file_edit.setText(settings.get_str('data.official_infos_file', 'resource/official_infos.json'))
    
    def apply_settings(self):
        """应用设置"""
        try:
            # 记录原始数据文件路径
            old_accounts_file = settings.get_str('data.accounts_file', 'resource/accounts.csv')
            old_official_infos_file = settings.get_str('data.official_infos_file', 'resource/official_infos.json')

            # 微信设置
            settings.set('wechat.wechat_path', self.wechat_path_edit.text())
            settings.set('wechat.load_delay', self.load_delay_spin.value())
            settings.set('wechat.is_maximize', self.maximize_check.isChecked())

            # 采集设置
            settings.set('task.limit_count', self.limit_count_spin.value())
            settings.set('task.stop_exist_count', self.stop_exist_count_spin.value())

            # 数据文件设置
            new_accounts_file = self.accounts_file_edit.text()
            new_official_infos_file = self.official_infos_file_edit.text()

            settings.set('data.accounts_file', new_accounts_file)
            settings.set('data.official_infos_file', new_official_infos_file)

            # 检查数据文件路径是否有变化
            data_files_changed = (
                old_accounts_file != new_accounts_file or
                old_official_infos_file != new_official_infos_file
            )

            if data_files_changed:
                # 通知主窗口刷新数据
                self.refresh_data_after_settings_change()
                CustomMessageBox.information(
                    self, "成功",
                    "设置已保存！\n数据文件路径已更新，相关数据列表将自动刷新。"
                )
            else:
                CustomMessageBox.information(self, "成功", "设置已保存")

        except Exception as e:
            CustomMessageBox.critical(self, "错误", f"保存设置失败: {str(e)}")

    def refresh_data_after_settings_change(self):
        """数据文件路径变更后刷新相关数据"""
        try:
            # 获取主窗口实例
            main_window = self.parent()
            if main_window and hasattr(main_window, 'account_manager'):
                # 刷新账号管理器的数据
                main_window.account_manager.load_accounts()

            if main_window and hasattr(main_window, 'data_viewer'):
                # 刷新数据查看器的统计信息
                if hasattr(main_window.data_viewer, 'stats_widget'):
                    main_window.data_viewer.stats_widget.update_stats()

        except Exception as e:
            print(f"刷新数据时出错: {e}")
    


    def accept(self):
        """确定按钮处理"""
        self.apply_settings()
        super().accept()
