"""
日志显示组件
"""
from PySide6.QtWidgets import QTextEdit, QVBoxLayout, QWidget, QPushButton, QHBoxLayout
from PySide6.QtCore import Qt, QDateTime
from PySide6.QtGui import QFont, QTextCursor


class LogWidget(QWidget):
    """日志显示组件"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()
    
    def setup_ui(self):
        """设置UI"""
        layout = QVBoxLayout(self)
        
        # 工具栏
        toolbar_layout = QHBoxLayout()
        
        self.clear_btn = QPushButton("清空日志")
        self.clear_btn.clicked.connect(self.clear_logs)
        
        self.save_btn = QPushButton("保存日志")
        self.save_btn.clicked.connect(self.save_logs)
        
        toolbar_layout.addWidget(self.clear_btn)
        toolbar_layout.addWidget(self.save_btn)
        toolbar_layout.addStretch()
        
        # 日志文本框
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        self.log_text.setFont(QFont("Consolas", 9))
        
        layout.addLayout(toolbar_layout)
        layout.addWidget(self.log_text)
    
    def add_log(self, message: str, level: str = "INFO"):
        """添加日志"""
        timestamp = QDateTime.currentDateTime().toString("yyyy-MM-dd hh:mm:ss")
        log_entry = f"[{timestamp}] [{level}] {message}"
        
        self.log_text.append(log_entry)

        # 自动滚动到底部
        cursor = self.log_text.textCursor()
        cursor.movePosition(QTextCursor.MoveOperation.End)
        self.log_text.setTextCursor(cursor)
    
    def clear_logs(self):
        """清空日志"""
        self.log_text.clear()
    
    def save_logs(self):
        """保存日志到文件"""
        from PySide6.QtWidgets import QFileDialog
        
        file_path, _ = QFileDialog.getSaveFileName(
            self,
            "保存日志文件",
            f"wechat_auto_log_{QDateTime.currentDateTime().toString('yyyyMMdd_hhmmss')}.txt",
            "文本文件 (*.txt);;所有文件 (*)"
        )
        
        if file_path:
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(self.log_text.toPlainText())
                self.add_log(f"日志已保存到: {file_path}")
            except Exception as e:
                self.add_log(f"保存日志失败: {str(e)}", "ERROR")
