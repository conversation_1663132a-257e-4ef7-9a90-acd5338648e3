"""
数据统计组件
"""
import os
import csv
from typing import Dict, List, Any
from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel,
                               QGroupBox, QGridLayout, QPushButton, QScrollArea)
from PySide6.QtCore import Qt, QTimer
from PySide6.QtGui import QFont


class DataStatsWidget(QWidget):
    """数据统计组件"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()
        self.setup_timer()
        self.update_stats()
    
    def setup_ui(self):
        """设置UI"""
        layout = QVBoxLayout(self)
        layout.setSpacing(10)
        layout.setContentsMargins(10, 10, 10, 10)

        # 设置扁平化样式
        self.setStyleSheet("""
            QWidget {
                background-color: #ffffff;
                font-family: 'Microsoft YaHei', 'Se<PERSON>e UI', <PERSON><PERSON>, sans-serif;
            }
            QGroupBox {
                font-weight: 500;
                border: 1px solid #e0e0e0;
                margin-top: 6px;
                padding-top: 10px;
                background-color: #ffffff;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 6px 0 6px;
                color: #333333;
                font-size: 12px;
                font-weight: 500;
            }
            QPushButton {
                background-color: #2196F3;
                color: white;
                border: none;
                padding: 6px 12px;
                font-weight: 400;
                min-width: 80px;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #1976D2;
            }
            QLabel {
                color: #333333;
                font-size: 12px;
            }
        """)

        # 标题
        # title_label = QLabel("数据统计")
        # title_label.setStyleSheet("""
        #     font-size: 10px;
        #     font-weight: 500;
        #     color: #333333;
        #     border-bottom: 1px solid #e0e0e0;
        # """)
        # layout.addWidget(title_label)
        
        # 统计信息组
        stats_group = QGroupBox("采集统计")
        stats_layout = QVBoxLayout(stats_group)
        stats_layout.setSpacing(8)

        # 创建统计卡片
        def create_stat_card(title, value_widget, color):
            card = QWidget()
            card.setStyleSheet(f"""
                QWidget {{
                    background-color: {color};
                }}
            """)
            card_layout = QHBoxLayout(card)
            card_layout.setContentsMargins(8, 6, 8, 6)

            # 标题
            title_label = QLabel(title)
            title_label.setStyleSheet("color: white; font-weight: 400; font-size: 11px;")
            card_layout.addWidget(title_label)

            card_layout.addStretch()

            # 数值
            value_widget.setStyleSheet("color: white; font-weight: 500; font-size: 16px;")
            card_layout.addWidget(value_widget)

            return card

        # 总记录数卡片
        self.total_records_label = QLabel("0")
        total_card = create_stat_card("总记录数", self.total_records_label, "#2196F3")
        stats_layout.addWidget(total_card)

        # 公众号数量卡片
        self.account_count_label = QLabel("0")
        account_card = create_stat_card("公众号数", self.account_count_label, "#4CAF50")
        stats_layout.addWidget(account_card)

        # 文件大小卡片
        self.file_size_label = QLabel("0 KB")
        size_card = create_stat_card("文件大小", self.file_size_label, "#00BCD4")
        stats_layout.addWidget(size_card)

        # 最后更新时间
        update_info = QWidget()
        update_layout = QHBoxLayout(update_info)
        update_layout.setContentsMargins(6, 3, 6, 3)

        update_text = QLabel("最后更新:")
        update_text.setStyleSheet("font-size: 11px; color: #666666; font-weight: 400;")
        update_layout.addWidget(update_text)

        self.last_update_label = QLabel("未知")
        self.last_update_label.setStyleSheet("font-size: 11px; color: #333333; font-weight: 400;")
        update_layout.addWidget(self.last_update_label)

        update_layout.addStretch()
        stats_layout.addWidget(update_info)

        layout.addWidget(stats_group)

        # 文件信息组
        files_group = QGroupBox("文件分布")
        files_layout = QVBoxLayout(files_group)
        files_layout.setSpacing(8)

        # 创建滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
        scroll_area.setStyleSheet("""
               QScrollArea {
                   border: none;
                   background-color: transparent;
               }
               QScrollBar:vertical {
                   border: none;
                   background: #f0f0f0;
                   width: 12px;
                   margin: 0px 0px 0px 0px;
               }
               QScrollBar::handle:vertical {
                   background: #c0c0c0;
                   border-radius: 2px;
                   min-height: 20px;
               }
               QScrollBar::handle:vertical:hover {
                   background: #a0a0a0;
               }
           """)

        # 创建内容容器
        self.files_content_widget = QWidget()
        self.files_content_widget.setStyleSheet("background-color: transparent;")

        # 内容布局
        self.files_content_layout = QVBoxLayout(self.files_content_widget)
        self.files_content_layout.setContentsMargins(8, 8, 8, 8)
        self.files_content_layout.setSpacing(4)

        self.files_info_label = QLabel("正在加载...")
        self.files_info_label.setStyleSheet("""
               font-size: 12px; 
               color: #333333;
               background-color: transparent;
           """)
        self.files_info_label.setWordWrap(True)
        self.files_info_label.setAlignment(Qt.AlignmentFlag.AlignTop)
        self.files_content_layout.addWidget(self.files_info_label)
        self.files_content_layout.addStretch()

        # 将内容容器设置为滚动区域的widget
        scroll_area.setWidget(self.files_content_widget)

        # 设置固定高度或最大高度
        scroll_area.setMaximumHeight(200)  # 可根据需要调整
        # 或者使用固定高度: scroll_area.setFixedHeight(200)

        files_layout.addWidget(scroll_area)

        layout.addWidget(files_group)

        # 数据质量组
        quality_group = QGroupBox("数据质量")
        quality_layout = QVBoxLayout(quality_group)
        quality_layout.setSpacing(6)

        # 重复率进度条
        duplicate_container = QWidget()
        duplicate_layout = QVBoxLayout(duplicate_container)
        duplicate_layout.setContentsMargins(0, 0, 0, 0)

        duplicate_header = QHBoxLayout()
        duplicate_title = QLabel("重复率")
        duplicate_title.setStyleSheet("font-weight: 400; color: #333333; font-size: 11px;")
        duplicate_header.addWidget(duplicate_title)

        self.duplicate_rate_label = QLabel("0%")
        self.duplicate_rate_label.setStyleSheet("font-weight: 500; color: #F44336; font-size: 11px;")
        duplicate_header.addWidget(self.duplicate_rate_label)
        duplicate_header.addStretch()

        duplicate_layout.addLayout(duplicate_header)

        # 重复率进度条
        from PySide6.QtWidgets import QProgressBar
        self.duplicate_progress = QProgressBar()
        self.duplicate_progress.setMaximum(100)
        self.duplicate_progress.setStyleSheet("""
            QProgressBar {
                border: 1px solid #e0e0e0;
                text-align: center;
                font-weight: 400;
                height: 16px;
                font-size: 10px;
            }
            QProgressBar::chunk {
                background-color: #F44336;
            }
        """)
        duplicate_layout.addWidget(self.duplicate_progress)
        quality_layout.addWidget(duplicate_container)

        # 有效链接率进度条
        valid_container = QWidget()
        valid_layout = QVBoxLayout(valid_container)
        valid_layout.setContentsMargins(0, 0, 0, 0)

        valid_header = QHBoxLayout()
        valid_title = QLabel("有效链接")
        valid_title.setStyleSheet("font-weight: 400; color: #333333; font-size: 11px;")
        valid_header.addWidget(valid_title)

        self.valid_link_rate_label = QLabel("0%")
        self.valid_link_rate_label.setStyleSheet("font-weight: 500; color: #4CAF50; font-size: 11px;")
        valid_header.addWidget(self.valid_link_rate_label)
        valid_header.addStretch()

        valid_layout.addLayout(valid_header)

        # 有效链接率进度条
        self.valid_progress = QProgressBar()
        self.valid_progress.setMaximum(100)
        self.valid_progress.setStyleSheet("""
            QProgressBar {
                border: 1px solid #e0e0e0;
                text-align: center;
                font-weight: 400;
                height: 16px;
                font-size: 10px;
            }
            QProgressBar::chunk {
                background-color: #4CAF50;
            }
        """)
        valid_layout.addWidget(self.valid_progress)
        quality_layout.addWidget(valid_container)

        layout.addWidget(quality_group)
        
        # 操作按钮
        button_layout = QHBoxLayout()
        
        self.refresh_btn = QPushButton("刷新统计")
        self.refresh_btn.clicked.connect(self.update_stats)
        button_layout.addWidget(self.refresh_btn)
        
        button_layout.addStretch()
        
        layout.addLayout(button_layout)
        
        layout.addStretch()
    
    def setup_timer(self):
        """设置定时器"""
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_stats)
        self.timer.start(30000)  # 每30秒更新一次

    def update_stats(self):
        """更新统计信息"""
        try:
            # 统计all.csv文件
            all_csv_stats = self.get_csv_stats("data/all.csv")
            
            # 更新显示
            self.total_records_label.setText(str(all_csv_stats['record_count']))
            self.account_count_label.setText(str(all_csv_stats['account_count']))
            self.file_size_label.setText(self.format_file_size(all_csv_stats['file_size']))
            self.last_update_label.setText(all_csv_stats['last_modified'])
            
            # 统计data目录下的文件
            data_files_info = self.get_data_files_info()
            self.files_info_label.setText(data_files_info)

            # 统计数据质量
            quality_stats = self.get_data_quality_stats()
            duplicate_rate = quality_stats['duplicate_rate']
            valid_rate = quality_stats['valid_link_rate']

            self.duplicate_rate_label.setText(f"{duplicate_rate:.1f}%")
            self.valid_link_rate_label.setText(f"{valid_rate:.1f}%")

            # 更新进度条
            self.duplicate_progress.setValue(int(duplicate_rate))
            self.valid_progress.setValue(int(valid_rate))
            
        except Exception as e:
            self.files_info_label.setText(f"统计失败: {str(e)}")
    
    def get_csv_stats(self, file_path: str) -> Dict[str, Any]:
        """获取CSV文件统计信息"""
        stats = {
            'record_count': 0,
            'account_count': 0,
            'file_size': 0,
            'last_modified': '未知'
        }
        
        if not os.path.exists(file_path):
            return stats
        
        try:
            # 文件大小
            stats['file_size'] = os.path.getsize(file_path)
            
            # 最后修改时间
            import time
            mtime = os.path.getmtime(file_path)
            stats['last_modified'] = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(mtime))
            
            # 读取CSV内容统计
            with open(file_path, 'r', encoding='utf-8') as f:
                reader = csv.reader(f)
                headers = next(reader, [])
                
                accounts = set()
                record_count = 0
                
                for row in reader:
                    record_count += 1
                    if len(row) > 0:  # 假设第一列是账号
                        accounts.add(row[0])
                
                stats['record_count'] = record_count
                stats['account_count'] = len(accounts)
        
        except Exception:
            pass
        
        return stats
    
    def get_data_files_info(self) -> str:
        """获取data目录文件信息"""
        data_dir = "data"
        
        if not os.path.exists(data_dir):
            return "data目录不存在"
        
        try:
            csv_files = [f for f in os.listdir(data_dir) if f.endswith('.csv')]
            
            if not csv_files:
                return "data目录下没有CSV文件"
            
            info_lines = [f"data目录下有 {len(csv_files)} 个CSV文件:"]
            
            total_size = 0
            total_records = 0
            
            for file in sorted(csv_files):
                file_path = os.path.join(data_dir, file)
                file_size = os.path.getsize(file_path)
                total_size += file_size
                
                # 统计记录数
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        reader = csv.reader(f)
                        next(reader, [])  # 跳过表头
                        record_count = sum(1 for _ in reader)
                        total_records += record_count
                        
                        info_lines.append(
                            f"• {file}: {record_count} 条记录, {self.format_file_size(file_size)}"
                        )
                except Exception:
                    info_lines.append(f"• {file}: 读取失败")
            
            info_lines.append("")
            info_lines.append(f"总计: {total_records} 条记录, {self.format_file_size(total_size)}")
            
            return "\n".join(info_lines)
        
        except Exception as e:
            return f"读取data目录失败: {str(e)}"

    def get_data_quality_stats(self) -> Dict[str, float]:
        """获取数据质量统计"""
        stats = {
            'duplicate_rate': 0.0,
            'valid_link_rate': 0.0
        }

        all_csv_path = "data/all.csv"
        if not os.path.exists(all_csv_path):
            return stats

        try:
            with open(all_csv_path, 'r', encoding='utf-8') as f:
                reader = csv.reader(f)
                headers = next(reader, [])

                all_links = []
                valid_links = 0
                total_records = 0

                for row in reader:
                    total_records += 1
                    if len(row) > 2:  # 假设链接在第3列
                        link = row[2].strip()
                        all_links.append(link)

                        # 检查是否为有效的微信文章链接
                        if link.startswith('https://mp.weixin.qq.com/s/'):
                            valid_links += 1

                if total_records > 0:
                    # 计算重复率
                    unique_links = len(set(all_links))
                    if len(all_links) > 0:
                        stats['duplicate_rate'] = ((len(all_links) - unique_links) / len(all_links)) * 100

                    # 计算有效链接率
                    stats['valid_link_rate'] = (valid_links / total_records) * 100

        except Exception:
            pass

        return stats

    def get_account_distribution(self) -> Dict[str, int]:
        """获取账号分布统计"""
        distribution = {}

        all_csv_path = "data/all.csv"
        if not os.path.exists(all_csv_path):
            return distribution

        try:
            with open(all_csv_path, 'r', encoding='utf-8') as f:
                reader = csv.reader(f)
                headers = next(reader, [])

                for row in reader:
                    if len(row) > 0:  # 假设账号在第1列
                        account = row[0].strip()
                        if account:
                            distribution[account] = distribution.get(account, 0) + 1

        except Exception:
            pass

        return distribution
    
    def format_file_size(self, size_bytes: int) -> str:
        """格式化文件大小"""
        if size_bytes == 0:
            return "0 B"
        
        units = ['B', 'KB', 'MB', 'GB']
        unit_index = 0
        size = float(size_bytes)
        
        while size >= 1024 and unit_index < len(units) - 1:
            size /= 1024
            unit_index += 1
        
        if unit_index == 0:
            return f"{int(size)} {units[unit_index]}"
        else:
            return f"{size:.1f} {units[unit_index]}"
    
    def get_summary(self) -> Dict[str, Any]:
        """获取统计摘要"""
        all_csv_stats = self.get_csv_stats("data/all.csv")
        
        return {
            "total_records": all_csv_stats['record_count'],
            "account_count": all_csv_stats['account_count'],
            "file_size": all_csv_stats['file_size'],
            "last_modified": all_csv_stats['last_modified'],
            "data_files_count": len([f for f in os.listdir("data") 
                                   if f.endswith('.csv')]) if os.path.exists("data") else 0
        }
